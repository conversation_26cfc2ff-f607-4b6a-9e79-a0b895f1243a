import { type TypedDocumentNode } from "@graphql-typed-document-node/core"
import {
  GraphQLClient,
  type RequestDocument,
  type Variables,
  type RequestOptions,
} from "graphql-request"
import {
  type VariablesAndRequestHeadersArgs,
  type RequestConfig,
  type GraphQLClientResponse,
} from "graphql-request/build/esm/types"

/**
 * Configuration for persisted query client wrapper
 */
export interface PersistedQueryGraphQLClientConfig {
  /**
   * Whether to enable persisted queries. When false, behaves like regular GraphQLClient
   * @default true
   */
  enabled?: boolean
  /**
   * Whether to include the full query as fallback (for development)
   * @default false
   */
  includeFallbackQuery?: boolean
  /**
   * Enable debug logging for persisted queries
   * @default false
   */
  debug?: boolean
}

/**
 * Check if the response contains a "PersistedQueryNotFound" error
 */
function isPersistedQueryNotFoundError(response: unknown): boolean {
  try {
    const responseObj = response as GraphQLClientResponse<any>
    if (responseObj?.errors) {
      return responseObj.errors.some(
        (error) =>
          error.extensions?.code === "PERSISTED_QUERY_NOT_FOUND" ||
          error.message?.includes("PersistedQueryNotFound") ||
          error.message?.includes("persisted query not found")
      )
    }
    return false
  } catch {
    return false
  }
}

/**
 * Check if an error is a persisted query not found error from various sources
 */
function isPersistedQueryError(error: unknown): boolean {
  // Check if it's a GraphQL response error
  if (isPersistedQueryNotFoundError(error)) {
    return true
  }

  // Check if it's an error object with message
  if (error && typeof error === "object" && "message" in error) {
    const message = (error as { message: string }).message
    return (
      message?.includes("PersistedQueryNotFound") ||
      message?.includes("persisted query not found")
    )
  }

  return false
}

/**
 * Extract hash from document's __meta__ property
 */
function extractHashFromDocument<T, V>(
  document: RequestDocument | TypedDocumentNode<T, V>
): string | undefined {
  if (typeof document === "string") {
    return undefined
  }

  // Check if document has __meta__.hash property
  const meta = (document as any)?.__meta__
  if (meta && typeof meta.hash === "string") {
    return meta.hash
  }

  return undefined
}

/**
 * Create a persisted query document with hash
 */
function createPersistedQueryDocument<T, V>(persistedDocumentId: string) {
  return {
    extensions: {
      persistedQuery: {
        version: 1,
        sha256Hash: persistedDocumentId,
      },
    },
  } as unknown as TypedDocumentNode<T, V>
}

/**
 * GraphQL client that extends the base GraphQLClient with persisted query support
 */
export default class PersistedQueryGraphQLClient extends GraphQLClient {
  private config: PersistedQueryGraphQLClientConfig

  constructor(
    url: string,
    requestConfig?: RequestConfig,
    config?: PersistedQueryGraphQLClientConfig
  ) {
    super(url, requestConfig)
    this.config = {
      enabled: true,
      includeFallbackQuery: false,
      debug: false,
      ...config,
    }

    this.debugLog("PersistedQueryGraphQLClient initialized", {
      enabled: this.config.enabled,
      includeFallbackQuery: this.config.includeFallbackQuery,
      debug: this.config.debug,
    })
  }

  /**
   * Log debug information if debug mode is enabled
   */
  private debugLog(message: string, data?: unknown): void {
    if (this.config.debug) {
      console.log(`[PersistedQueryGraphQLClient] ${message}`, data)
    }
  }

  /**
   * Execute persisted query request with retry logic using options signature
   */
  private async executePersistedQueryWithOptions<
    T,
    V extends Variables = Variables
  >(options: RequestOptions<V, T>): Promise<T> {
    // If persisted queries are disabled, use regular GraphQL client behavior
    if (!this.config.enabled) {
      this.debugLog("Persisted queries disabled, using regular GraphQL client")
      return super.request<T, V>(options)
    }

    // If includeFallbackQuery is enabled, send full query immediately
    if (this.config.includeFallbackQuery) {
      this.debugLog("Using fallback query (includeFallbackQuery enabled)")
      return super.request<T, V>(options)
    }

    // Try to extract hash from document's __meta__ property
    const persistedDocumentId = extractHashFromDocument(options.document)

    // If no hash found in document, fall back to normal query
    if (!persistedDocumentId) {
      this.debugLog(
        "No hash found in document __meta__, falling back to full query"
      )
      return super.request<T, V>(options)
    }

    this.debugLog("Found persisted document hash", { persistedDocumentId })

    try {
      // First attempt: send only the persisted query hash
      const persistedQueryDocument = createPersistedQueryDocument<T, V>(
        persistedDocumentId
      )

      const persistedOptions: RequestOptions<V, T> = {
        ...options,
        document: persistedQueryDocument,
      }

      this.debugLog("Attempting persisted query request")
      return await super.request<T, V>(persistedOptions)
    } catch (error) {
      // Check if it's a persisted query not found error
      if (isPersistedQueryError(error)) {
        this.debugLog(
          "Persisted query not found on server, retrying with full query",
          { error }
        )
        // Retry with the full query
        return super.request<T, V>(options)
      }

      this.debugLog("Non-persisted query error occurred", { error })
      // Re-throw other errors
      throw error
    }
  }

  /**
   * Request method with persisted query support
   * Supports both document with variables/headers signature and options signature
   */
  async request<T, V extends Variables = Variables>(
    document: RequestDocument | TypedDocumentNode<T, V>,
    ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
  ): Promise<T>
  async request<T, V extends Variables = Variables>(
    options: RequestOptions<V, T>
  ): Promise<T>
  async request<T, V extends Variables = Variables>(
    documentOrOptions:
      | RequestDocument
      | TypedDocumentNode<T, V>
      | RequestOptions<V, T>,
    ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
  ): Promise<T> {
    // Check if first argument is options object
    if (
      typeof documentOrOptions === "object" &&
      documentOrOptions !== null &&
      "document" in documentOrOptions
    ) {
      // Handle options signature
      const options = documentOrOptions as RequestOptions<V, T>
      return this.executePersistedQueryWithOptions<T, V>(options)
    } else {
      // Handle document with variables/headers signature
      const document = documentOrOptions as
        | RequestDocument
        | TypedDocumentNode<T, V>
      const options = {
        document,
        variables: variablesAndRequestHeaders?.[0],
        requestHeaders: variablesAndRequestHeaders?.[1],
      } as RequestOptions<V, T>

      return this.executePersistedQueryWithOptions<T, V>(options)
    }
  }
}
