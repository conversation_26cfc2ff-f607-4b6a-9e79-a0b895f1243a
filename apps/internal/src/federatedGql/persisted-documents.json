{"aa8bd20eae392d0c40793020ecdd3c6249af0db275fc8e9590728cea88907257": "query getKBKIList($codes: [String], $level: KBKILevel, $page: Int, $parentCodes: [String], $perPage: Int, $search: String) { getAllKBKI( level: $level search: $search parentCodes: $parentCodes page: $page perPage: $perPage codes: $codes ) { items { classification code id level parentCode title } perPage total } }", "671ddf52fe83d7f8ef53f48289efb42eed9ed0c984d6beb1ff4b0a310914e98c": "query getKBLIList($level: KBLILevel!, $page: Int, $perPage: Int, $search: String) { getAllKBLI(search: $search, level: $level, page: $page, perPage: $perPage) { items { classification code id level parentCode title } perPage total } }", "158069867effa1e6bcb249cfc76f74cde6a2333f1e74f7c6bd704b77c5890ada": "query getProductChangesList($input: ProductChangesListInput!) { productChangesList(input: $input) { ... on GenericError { __typename code message reqId } ... on ListProductChanges { __typename currentPage items { createdAt formattedCreatedDate statusChanges { after before } type userInfo { id name username } version } lastPage perPage total } } }", "ab6414d904d82fa95bc5856d8ab083dd37619461f1c49dd703ae9100910afa39": "query getProductChangesDetail($input: ProductChangesInput!) { productChangesDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductChanges { after { actionReasons { description enum reason } brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber statusName url } category { id isActive name } categoryType createdAt deletedAt description hasVat id images { createdAt deletedAt id imageUrl updatedAt } isActive isSellerUMKK kbki labels masterProductId name nie { category class companyName expirationDate hsCode manufacturerAddress manufacturerCountry manufacturerName nib nie npwp packaging permitClassification productName productType publishedDate riskClass size subCategory } pdn { countryCode countryName laborDescription laborType locationDescription locationType materialDescription materialType type } preOrder { createdAt deletedAt sla updatedAt } prices(regionCode: \"\") { maxPrice maxPriceWithTax minPrice minPriceWithTax minPurchase productWholesalePrices { createdAt deletedAt id minQuantity price priceWithTax updatedAt } } productAddOns { createdAt deletedAt description id name objectType productAddOnVariants { createdAt deletedAt description id name price priceWithTax updatedAt } tax { ppnPercentage ppnTypes } type updatedAt } productInformations { additionalInformations { createdAt customFormFieldId deletedAt id name updatedAt value } documents { createdAt customFormFieldId deletedAt id name updatedAt value } mainInformations { createdAt customFormFieldId deletedAt id name updatedAt value } } productOptions { id option optionLevel valueIds values } sellerId sellerLocation { cityAreaCode cityId cityName districtAreaCode districtId districtName isFTZ paramId provinceAreaCode provinceId provinceName villageAreaCode villageId villageName villagePostalCode } sellerName sellerVillageAreaCode shipping { dimensionUnit hasSellerShipping height length weight weightUnit width } shippingOption slug sni { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber url } status stockAccumulation stockUnit { primaryUnit secondaryUnit value } tax { ppnBmId ppnBmPercentage ppnBmType ppnPercentage ppnTypes } tkdn { bmpDate bmpExpirationDate bmpValue brand companyName date description email expirationDate hs number producerName productType specificationId standard tkdnBmp type url value } type unitSold updatedAt username variants { createdAt deletedAt id imageUrl isActive optionValues options price priceWithTax sellerId sku sortOrder stock updatedAt } version videoSource videoUrl } before { actionReasons { description enum reason } brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber statusName url } category { id isActive name } categoryType createdAt deletedAt description hasVat id images { createdAt deletedAt id imageUrl updatedAt } isActive isSellerUMKK kbki labels masterProductId name nie { category class companyName expirationDate hsCode manufacturerAddress manufacturerCountry manufacturerName nib nie npwp packaging permitClassification productName productType publishedDate riskClass size subCategory } pdn { countryCode countryName laborDescription laborType locationDescription locationType materialDescription materialType type } preOrder { createdAt deletedAt sla updatedAt } prices(regionCode: \"\") { maxPrice maxPriceWithTax minPrice minPriceWithTax minPurchase productWholesalePrices { createdAt deletedAt id minQuantity price priceWithTax updatedAt } } productAddOns { createdAt deletedAt description id name objectType productAddOnVariants { createdAt deletedAt description id name price priceWithTax updatedAt } tax { ppnPercentage ppnTypes } type updatedAt } productInformations { additionalInformations { createdAt customFormFieldId deletedAt id name updatedAt value } documents { createdAt customFormFieldId deletedAt id name updatedAt value } mainInformations { createdAt customFormFieldId deletedAt id name updatedAt value } } productOptions { id option optionLevel valueIds values } sellerId sellerLocation { cityAreaCode cityId cityName districtAreaCode districtId districtName isFTZ paramId provinceAreaCode provinceId provinceName villageAreaCode villageId villageName villagePostalCode } sellerName sellerVillageAreaCode shipping { dimensionUnit hasSellerShipping height length weight weightUnit width } shippingOption slug sni { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber url } status stockAccumulation stockUnit { primaryUnit secondaryUnit value } tax { ppnBmId ppnBmPercentage ppnBmType ppnPercentage ppnTypes } tkdn { bmpDate bmpExpirationDate bmpValue brand companyName date description email expirationDate hs number producerName productType specificationId standard tkdnBmp type url value } type unitSold updatedAt username variants { createdAt deletedAt id imageUrl isActive optionValues options price priceWithTax sellerId sku sortOrder stock updatedAt } version videoSource videoUrl } changes createdAt formattedCreatedDate version } } }", "aefbc80ae9efd6f4e5c1951bb5551fb2d63604736535bdfdc5c1a0a3ba7326eb": "query getSNIList($query: SearchSNIInput) { getSearchSNI(query: $query) { ... on GenericError { __typename code message reqId } ... on SNIList { currentPage items { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber } lastPage perPage total } } }", "e043f3c806bcb315ecd75fcec54b10e245bbca2e31f2531f60807288a30787ec": "query getListPpnBm { ppnBm { ... on GenericError { __typename code message reqId } ... on PpnBm { __typename items { description id level name parentId percentage } } } }", "76be620e373fee0a2ed2e2242cb9e33a8ce17683ab02578e3e5977738349bdca": "query getAllRegionV2($level: RegionLevel! = PROVINCE, $parentAreaCodes: [String!]) { getAllRegionV2( query: {filter: {level: $level, parentAreaCodes: $parentAreaCodes}} ) { ... on GenericError { __typename code message reqId } ... on RegionAllList { __typename items { areaCode id level name parentId postalCode } } } }", "4ce7fb6d637af06818b6547344cd8914d8a09caae4448a12266ac06a1d71f70f": "query negotiationHistory($input: NegotiationDetailInput!) { negotiationDetailV2(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationDetailResponse { __typename latestTotal negotiation { items { addOns { addOnDetail { addOnVariantId originalPrice tax { ppnPercentage } type } agreementPrice history { createdAt isNewProposal newPrice oldPrice proposerRole } } agreementPrice history { createdAt isNewProposal newPrice oldPrice proposerRole } itemDetail { originalPrice productId productLabels productVariantId qty snapshot { addOns { name productAddOnVariants { id } } images { imageUrl } name pdn { type } preOrder { sla } prices { selectedRegionPrice { regionName } } shipping { weight weightUnit } stockUnit { primaryUnit } tax { ppnBmPercentage ppnPercentage } type } } } shipments { agreementPrice history { createdAt isNewProposal newPrice oldPrice proposerRole } shipmentDetail { deliveryOrder { detailPrice { ppnPercentage } } lastFee lastInsuranceFee originalFee originalInsuranceFee shipmentTax { ppn ppnInsuranceFee ppnPercentage ppnPercentageInsurance } } } } order { sellerInfo { sellerId } shipments { lastFee lastInsuranceFee provider shipmentTax { ppn ppnInsuranceFee ppnPercentage ppnPercentageInsurance } } } originalTotal status } } }", "128b78ce0a52ded5b1a12e77b0e1233b062c71ea168f1cfc43367458e1c1f733": "query getMultishipmentNegotiationHistory($input: NegotiationDetailInput!) { negotiationDetailV3(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationDetailV3Response { __typename expiryTime latestTotal negotiation { items { addOns { addOnDetail { addOnVariantId id lastPrice lastPrice originalPrice originalTax { ppn ppnBm } qty tax { ppn ppnBm ppnBmPercentage ppnPercentage taxableRate } type type } agreementPrice componentId history { createdAt createdAt isNewProposal negotiationId negotiationId newPrice newPriceTax { ppn ppnBm taxableRate } oldPrice proposerRole } } agreementPrice agreementPrice componentId componentId history { createdAt isNewProposal negotiationId newPrice newPriceTax { ppn ppnBm taxableRate } oldPrice proposerRole } itemDetail { addOns { addOnVariantId id } lastPrice originalPrice originalTax { ppn ppnBm } productId productLabels productVariantId qty snapshot { addOns { name productAddOnVariants { id name } type } consolidation { name pricingScheme } id images { imageUrl } name preOrder { sla } prices { selectedRegionPrice { regionName } } prices { selectedRegionPrice { regionName } } shipping { weight weightUnit } slug stockUnit { primaryUnit } tax { ppnBmPercentage ppnPercentage ppnTypes taxableRate } type type username variants { optionValues } variants { optionValues } } tax { ppn ppnBm taxableRate } } } paymentSchemeRequest { componentId history { createdAt isNewProposal negotiationId newScheme { createdAt id scheme } oldScheme { createdAt id scheme } proposerRole reason } } payments { componentId history { createdAt isNewProposal negotiationId newPrice newShipments { id shipmentId } oldPrice oldShipments { id number shipmentId } proposerRole proposerRole } type } payments { componentId history { createdAt isNewProposal negotiationId newPrice newShipments { id number shipmentId } oldPrice oldShipments { id number shipmentId } proposerRole } type } shipments { agreementPrice componentId history { createdAt isNewProposal negotiationId newPrice newPriceTax { ppn ppnBm taxableRate } oldPrice proposerRole } lastFee lastPaymentId lastTax { ppn ppnInsuranceFee } negotiationDeliveryNumber shipmentDetail { deliveryOrder { deliveryNumber destination { address cityName districtName label postalCode provinceName villageAreaCode villageName } id requestDateArrival requestDateArrivalEnd requestDateArrivalStart totalQtyProduct volumetric { weight weightUnit } } id lastFee lastInsuranceFee notes number originalFee originalInsuranceFee originalShipmentTax { ppn ppnInsuranceFee } requestArrivalDate shipmentTax { ppn ppnInsuranceFee ppnPercentage ppnPercentageInsurance taxableRate } } } } order { buyerInfo { assignedUser { userInfo { id name } } } downPayment { createdAt documentId id status updatedAt } id isFtz items { addOns { id lastPrice qty tax { ppnPercentage taxableRate } } id lastPrice qty tax { ppnBmPercentage ppnPercentage taxableRate } } orderKey orderNumber paymentScheme paymentSchemeRequest payments { deliveryOrderGroupId id phase } roundingMethod sellerInfo { sellerId } shipmentSummary { provider service totalShipmentInsuranceFee totalShipmentOriginalFee totalShipmentOriginalInsuranceFee totalShipmentOriginalPrice totalShipmentTax { ppnPercentage ppnPercentageInsurance taxableRate } } status totalShipment type useRetention } orderNegotiationId originalTotal reasons { cancelReason { reason reasonDescription timestamp } rejectReason { reason reasonDescription timestamp } } status timestamp timestamp } } }", "5d6cd1e4365dbf235182de1c834d85ecc14871e1a109837dd66843212a077016": "query getMultishipmentNegotiationHistoryDev($input: NegotiationDetailInput!) { negotiationDetailV3(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationDetailV3Response { __typename expiryTime latestTotal negotiation { items { addOns { addOnDetail { addOnVariantId id lastPrice lastPrice originalPrice originalTax { ppn ppnBm } qty tax { ppn ppnBm ppnBmPercentage ppnPercentage taxableRate } type type } agreementPrice componentId history { createdAt createdAt isNewProposal negotiationId negotiationId newPrice newPriceTax { ppn ppnBm taxableRate } oldPrice proposerRole } } agreementPrice agreementPrice componentId componentId history { createdAt isNewProposal negotiationId newPrice newPriceTax { ppn ppnBm taxableRate } oldPrice proposerRole } itemDetail { addOns { addOnVariantId id } lastPrice originalPrice originalTax { ppn ppnBm } productId productLabels productVariantId qty snapshot { addOns { name productAddOnVariants { id name } type } consolidation { name pricingScheme } id images { imageUrl } name preOrder { sla } prices { selectedRegionPrice { regionName } } prices { selectedRegionPrice { regionName } } shipping { weight weightUnit } slug stockUnit { primaryUnit } tax { ppnBmPercentage ppnPercentage ppnTypes taxableRate } type type username variants { optionValues } variants { optionValues } } tax { ppn ppnBm taxableRate } } } paymentSchemeRequest { componentId history { createdAt isNewProposal negotiationId newScheme { createdAt id scheme } oldScheme { createdAt id scheme } proposerRole reason } } payments { componentId history { createdAt isNewProposal negotiationId newPrice newShipments { id shipmentId } oldPrice oldShipments { id number shipmentId } proposerRole proposerRole } type } payments { componentId history { createdAt isNewProposal negotiationId newPrice newShipments { id number shipmentId } oldPrice oldShipments { id number shipmentId } proposerRole } type } shipments { agreementPrice componentId history { createdAt isHigherThanLastPrice isNewProposal negotiationId newPrice newPriceTax { ppn ppnBm taxableRate } oldPrice proposerRole } lastFee lastPaymentId lastTax { ppn ppnInsuranceFee } negotiationDeliveryNumber shipmentDetail { deliveryOrder { deliveryNumber destination { address cityName districtName label postalCode provinceName villageAreaCode villageName } id requestDateArrival requestDateArrivalEnd requestDateArrivalStart totalQtyProduct volumetric { weight weightUnit } } id lastFee lastInsuranceFee notes number originalFee originalInsuranceFee originalShipmentTax { ppn ppnInsuranceFee } requestArrivalDate shipmentTax { ppn ppnInsuranceFee ppnPercentage ppnPercentageInsurance taxableRate } } } } order { buyerInfo { assignedUser { userInfo { id name } } } downPayment { createdAt documentId id status updatedAt } id isFtz items { addOns { id lastPrice qty tax { ppnPercentage taxableRate } } id lastPrice qty tax { ppnBmPercentage ppnPercentage taxableRate } } orderKey orderNumber paymentScheme paymentSchemeRequest payments { deliveryOrderGroupId id phase } roundingMethod sellerInfo { sellerId } shipmentSummary { provider service totalShipmentInsuranceFee totalShipmentOriginalFee totalShipmentOriginalInsuranceFee totalShipmentOriginalPrice totalShipmentTax { ppnPercentage ppnPercentageInsurance taxableRate } } status totalShipment type useRetention } orderNegotiationId originalTotal reasons { cancelReason { reason reasonDescription timestamp } rejectReason { reason reasonDescription timestamp } } status timestamp timestamp } } }", "d0731e4b46ceb8f946ac7492bda63f0ad7e195173ef307c7bbd8e1a206de8269": "query getNegotiationShipmentSummary($input: NegotiationShipmentSummaryInput!) { negotiationShipmentSummary(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationShipmentSummaryResponse { __typename shipments { addOnPriceAfterTax addOnQty deliveryOrder { deliveryNumber destination { address cityName districtName label postalCode provinceName villageName } requestDateArrivalEnd requestDateArrivalNotes requestDateArrivalStart totalQtyProduct volumetric { weight } } id negotiationDeliveryNumber paymentId productPriceAfterTax shipmentFeeAfterTax subTotalAfterTax } totalAfterTax totalInsuranceFeeAfterTax totalShipmentFeeAfterTax } } }", "29923fe757325f87ca8ed5263f6bb10c29fcda197e2752c523536fb7867488e9": "query getNegotiationShipmentList($input: NegotiationShipmentListInput! = {}) { negotiationShipmentList(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationShipmentListResponse { __typename items { deliveryOrder { deliveryNumber destination { address cityName districtName label postalCode provinceName villageName } id requestDateArrivalEnd requestDateArrivalNotes requestDateArrivalStart } negotiationDeliveryNumber shipmentId subTotalAfterTax } total } } }", "4c74220eebeb8c19edaa29515cca457422bab9056a6e512a570f06cd7e666cd4": "query getOrderTotalSummary($input: OrderTotalSummaryInput!) { orderTotalSummary: orderTotalSummaryV2(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderTotalSummaryResponse { __typename grandTotalAfterTax totalAddOn { totalPpn totalPrice totalQty } totalAllPpn totalAllPpnBm totalProduct { totalPpn totalPpnBm totalPrice totalQty } totalShipment { totalInsuranceFee totalPpn totalPpnInsuranceFee totalPrice } } } }", "6a7e87c56025a6c17abddf822405bba14800d623b37e034d2288fabfdf144f22": "query getProfile { me { ... on GenericError { __typename message reqId } ... on UserInfo { __typename email id name phone profile { nik nip } profileStatus status username } } }", "4c973e92fb14c60ef6c79afd551162c46378c43d56d93f26117cb74a2e366d45": "query getInstitutionById($id: String!) { getInstitutionByID(id: $id) { ... on GenericError { __typename code message reqId } ... on Institution { __typename company { bentukUsaha bidangIndustri id name status } institution { institutionType } klpd { jenisKlpd kodeKlpd namaKlpd namaKlpdEcatalog namaKlpdPadi namaKlpdSakti namaKlpdSipd namaKlpdSirup } satker { institutionId jenisSatker kodeSatker namaSatker namaSatkerEcatalog namaSatkerPadi namaSatkerSakti namaSatkerSipd namaSatkerSirup namaSatkerTrx } } } }", "f6fc0b6636593698ee86cf03f72169f3917a265af1e089fb001c5166dc28ac17": "query getPersonaNonpenyediaByID($id: String!) { getPersonaNonpenyediaByID(id: $id) { ... on GenericError { __typename code message reqId } ... on PersonaNonPenyedia { __typename klpd { namaKlpd } persona { penyediaJabatan unit } satker { alamat jenisSatker namaSatker npwp } } } }", "8a6510b06220ba72e9217297481033fa794821b3151ba8d7680436d935cefeaa": "mutation createCategory($input: CreateCategoryInput!) { createCategory(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductCategory { id } } }", "6f4de662dc64e915f1b3d169d1657529e78589c6145b0f594a572fc5984cd851": "mutation updateCategory($id: ID!, $input: UpdateCategoryInput!) { updateCategory(id: $id, input: $input) { ... on GenericError { __typename code message reqId } ... on ProductCategory { id } } }", "0e35e3011296bfd9694ad5b67fa638f0355dca6f2add0bff839220dd931da43e": "query getProductCategory($id: ID!) { getProductCategory(id: $id) { ... on GenericError { __typename code message reqId } ... on ProductCategory { additionalInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } additionalInfoCustomFormId allowAddOnTaxFree allowPpnBmProduct allowedPpnPercentage allowedTransactionMethod createdAt curationEnabled curatorKLPDCode curatorRoles curatorSection deletedAt documentCustomFormId documents { customFormId id mandatory name order showOnLogin type } hasVat hetPriceEnabled icon id isAdditionalFeeAllowed kbki { classCodes commodityCodes commodityGroupCodes divisionCodes groupCodes sectionCodes subclassCodes } kbli level masterProductEnabled name nieEnabled parentId ppnPercentage primaryInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } primaryInfoCustomFormId productType productType slug subcategoriesCount type updatedAt zoningPriceEnabled } } }", "f005a4ada820ceaa41b11ae627d0d473b136559655d5d6f551419e55d4108cf6": "query getProductCategoryDev($id: ID!) { getProductCategory(id: $id) { ... on GenericError { __typename code message reqId } ... on ProductCategory { additionalInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } additionalInfoCustomFormId allowAddOnTaxFree allowPpnBmProduct allowedPpnPercentage allowedTransactionMethod createdAt curationEnabled curatorKLPDCode curatorRoles curatorSection deletedAt documentCustomFormId documents { customFormId id mandatory name order showOnLogin type } hasVat hetPriceEnabled icon id isAdditionalFeeAllowed kbki { classCodes commodityCodes commodityGroupCodes divisionCodes groupCodes sectionCodes subclassCodes } kbli level masterProductEnabled name nieEnabled parentId ppnPercentage primaryInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } primaryInfoCustomFormId productType productType slug subcategoriesCount type updatedAt zoningPriceEnabled } } }", "a1ee7ef9cba73ad7d784fe6b86d545e4c7ef0f33471d48f0f2cad3dda20f2924": "query getProductCategoryCanary($id: ID!) { getProductCategory(id: $id) { ... on GenericError { __typename code message reqId } ... on ProductCategory { additionalInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } additionalInfoCustomFormId allowAddOnTaxFree allowPpnBmProduct allowedPpnPercentage allowedTransactionMethod createdAt curationEnabled curatorKLPDCode curatorRoles curatorSection deletedAt documentCustomFormId documents { customFormId id mandatory name order showOnLogin type } hasVat hetPriceEnabled icon id isAdditionalFeeAllowed kbki { classCodes commodityCodes commodityGroupCodes divisionCodes groupCodes sectionCodes subclassCodes } kbli level masterProductEnabled name nieEnabled parentId ppnPercentage primaryInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } primaryInfoCustomFormId productType productType slug subcategoriesCount type updatedAt zoningPriceEnabled } } }", "21daf8ae19d833dfc74b8e319902a1d64bbebc5ef0a2f2825c4609f2982e0ce1": "mutation deleteCategory($id: ID!) { deleteCategory(id: $id) { ... on GenericError { __typename code message reqId } ... on StatusResponse { __typename data { status } } } }", "754eae485c8e454d2cfc981e621abdc23b5c3abfd63466ffc6b818c2f9b81d1a": "mutation deleteMasterProduct($id: ID!) { deleteMasterProduct(id: $id) { ... on GenericError { __typename code message reqId } ... on StatusResponse { __typename data { status } } } }", "def03b4cde30510c28724480624d88d14979d0b7dc3c4d8983372623212078b5": "query getMasterProductById($id: ID!) { getMasterProduct(id: $id) { ... on GenericError { __typename code message reqId } ... on MasterProduct { brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber statusName url } category { id name } id images { createdAt deletedAt id imageUrl updatedAt } informations { additionalInformations { createdAt customFormFieldId deletedAt id name updatedAt value } mainInformations { createdAt customFormFieldId deletedAt id name updatedAt value } } kbki name nationalMaximumRetailPrice nie { category class companyName expirationDate hsCode manufacturerAddress manufacturerCountry manufacturerName nib nie npwp packaging permitClassification productName productType publishedDate riskClass size subCategory } pdn { laborDescription laborType locationDescription locationType materialDescription materialType type } regionPrices { childRegionPrices { id maximumRetailPrice parentRegionCode regionCode regionLevel regionName } id maximumRetailPrice parentRegionCode regionCode regionLevel regionName } sni { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber } tkdn { bmpDate bmpExpirationDate bmpValue brand companyName date description email expirationDate hs number producerName productType specificationId standard type url value } type videoSource videoUrl } } }", "78cad79700c33ecb6f1c7a170cb28b103feb2ad8b2e15c4978084ce9e51e088d": "query getNIE($nie: String!) { getNIE(query: {nie: $nie}) { ... on GenericError { __typename code message reqId } ... on NIE { expirationDate nie permitClassification } } }", "f9c5d359a1b459d47ac681a3763c78e69bf365b38ecade0c0fa121456e932497": "query getAllTKDNByNumber($number: String) { getSearchTKDN(number: $number) { ... on GenericError { __typename code message reqId } ... on ListTKDN { __typename items { brand description hs number productType specificationId standard type value } } } }", "cd9cdb3ea14c5a514a469bc9d13d7f24a4c87030ff1e54075929943520d33294": "query getAllProductCategory($input: ListCategoryInput) { getAllProductCategory(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductCategories { __typename currentPage items { icon id level name parentId primaryInfoCount productCount slug subcategoriesCount } lastPage perPage total } } }", "c0bfd86759bf426bc53d3377135f0204a4586eb22da5f26b51564d39690f9f3c": "query getAllMasterProduct($categoryId: ID!) { getListMasterProduct(categoryId: $categoryId) { ... on GenericError { __typename code message reqId } ... on ListMasterProductRes { __typename items { id images { id imageUrl } name } } } }", "2d90ff96f280a9c53aec6fb51a322507bb1cc70ea4ce7d10aac03524b60663c4": "query getProductCountByCategory($input: BaseProductFilter) { sellerProductCount(input: $input) { ... on GenericError { __typename code message reqId } ... on SellerProductCountRes { __typename active inReview } } }", "b2d98ae2b40ce75ecb916a4dfae2ff2293636f0449b193bc207eb536d07c64c5": "query getListPpn { listPpn { ... on GenericError { __typename code message reqId } ... on ListPpn { __typename items { description ppnPercentage } } } }", "12a63ebf1bcc6c1f4607ae3ff061f65927e8be56d991ff01267b5cb57cc8f021": "query getListKLPD($filter: KlpdFilter!, $pagination: AccountPagination!) { klpd(pagination: $pagination, filter: $filter) { ... on GenericError { __typename code message reqId } ... on KlpdResult { __typename items { kodeKlpd namaKlpd } paginationInfo { currentPage lastPage nextPage perPage prevPage total } } } }", "220bb83516e90ae8c38945ea0eb72da17b17c2ece2b8b54755a36f08d0be6fd9": "query getCuratorRoles($input: CuratorRolesInput!) { curatorRoles(input: $input) { ... on CuratorRoles { __typename items { id klpdCode name } } ... on GenericError { __typename code message reqId } } }", "570e9ca9269aef669395d2e8edfc6c58a88fcf4abac171b1792f5e9229625ff7": "mutation createMasterProduct($input: CreateMasterProductInput) { createMasterProduct(input: $input) { ... on GenericError { __typename code message reqId } ... on MasterProduct { id name } } }", "b9f61e52e97eb7e9da43a5cfa81b266256c14621ca95cc0dde0cf7950ac8d72b": "mutation updateMasterProduct($id: ID!, $input: UpdateMasterProductInput) { updateMasterProduct(id: $id, input: $input) { ... on GenericError { __typename code message reqId } ... on MasterProduct { id name } } }", "8a24076562f0e02966b1fb830f2c6f1ab22447c5d153b2b1f5174cc3cdcef936": "query getBrand($applicationNumber: String!) { getBrand(applicationNumber: $applicationNumber) { ... on Brand { applicationNumber brandName url } ... on GenericError { __typename code message reqId } } }", "b40684ff392a6860a8ee390f967ab11fcb110abf16f8d5474ecc69c63e06a118": "query getOrderStatusHistorySummary($id: String!, $sort: SortDirection) { orderStatusHistorySummary(id: $id, sort: $sort) { ... on GenericError { __typename code message reqId } ... on OrderStatusHistorySummaryResponse { __typename orderStatus { status { ... on AddendumStatusType { __typename addendumStatus: value } ... on NegotiationStatusType { __typename negotiationStatus: value } ... on OrderStatusType { __typename orderStatus: value } } statusActorRole timestamp } } } }", "97bce62bf4f4f83dc823a7934154ad48baa4d352e5953c43a5975e34d7f9e75b": "mutation submitMasterProductCurationDetail($input: SubmitCurationDetailInput!) { submitMasterProductCurationDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on SubmitMasterProductCurationDetailResponse { __typename curationDetail { curatorRoleId curatorRoleName description id notes productCurationId rejectReasons { curationType description enum id reason } status } masterProductStatus } } }", "702dc32b94e55315aca6c47d8e7d54ab728be9a9449856cecec0ccd31d86316b": "query getMasterProductV2($masterProductV2Id: ID!) { masterProductV2(id: $masterProductV2Id) { ... on GenericError { code message reqId } ... on MasterProductV2 { access brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber status statusName url } category { id name } createdAt curation { details { curatorRoleId curatorRoleName description id notes productCurationId rejectReasons { curationType description enum id reason } status } id masterProductId status } description id images { createdAt deletedAt id imageUrl updatedAt } informations { additionalInformations { createdAt customFormFieldId deletedAt id name updatedAt value } documentInformations { createdAt customFormFieldId deletedAt id name updatedAt value } mainInformations { createdAt customFormFieldId deletedAt id name updatedAt value } } kbki labels maxPriceUpperBound minPriceLowerBound name options { id option optionLevel values { id sortOrder value } } pdn { countryCode countryName laborDescription laborType locationDescription locationType materialDescription materialType type } priceScheme principal { id name source } principalSource regionPrices { id parentRegionCode priceLowerBound priceUpperBound regionCode regionLevel regionName } sellerCompany { bentukUsaha bidangIndustri blacklist { endDate startDate status } companyAddresses { fullAddress id isMainAddress label latitude longitude notes phoneNumber postalCode regionDetail { cityName districtName provinceName villageName } villageAreaCode } id institusiId isCabang isUmkk jenisPerusahaan name npwp picChat picTtd rekanId shippingAddresses { fullAddress id isMainAddress label latitude longitude notes phoneNumber postalCode receiverName regionDetail { cityName districtName provinceName villageName } snapshotId villageAreaCode } skalaUsaha slug status website } shipping { dimensionUnit height length weight weightUnit width } sni { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber status url } status tax { ppnBmId ppnBmPercentage ppnPercentage } tkdn { bmpDate bmpExpirationDate bmpValue brand companyName comparisons { component label productInfo tkdnInfo } date description email expirationDate hs number producerName productType score scoringCategory scoringReason specificationId standard status tkdnBmp type url value } type variants { createdAt deletedAt id isActive optionValues options priceLowerBound priceUpperBound sku sortOrder updatedAt } } } }", "15a5bb0c14b07a2e98279afce97849a5dacbf57432395e719d99254fa66ad598": "query getDaftarKBLI($codes: [String], $page: Int, $perPage: Int) { getAllKBLI(codes: $codes, page: $page, perPage: $perPage) { items { code description title } perPage total } }", "110542acdc27b4fffa67a2c621ef5c00d5b5553fbdec408a21b157d11699508c": "query getCompanyPublicKbliCode($filter: CompanyPublicFilter!) { companyPublic(filter: $filter) { ... on CompanyPublicResult { __typename companies { oss { project { kbli { id kbli name } } } } } ... on GenericError { __typename code message reqId } } }", "523baa2b389cf2c5ed4eb743986342087d5eed014dc23638bbe451e5589ea101": "query getSellerInfoByID($id: String!) { getInstitutionByID(id: $id) { ... on GenericError { __typename code message reqId } ... on Institution { __typename company { bentukUsaha companyAddresses { fullAddress isMainAddress label notes phoneNumber postalCode villageAreaCode } id isCabang isUmkk izin { isVerify izinUsahaExpired izinUsahaInstitusi izinUsahaNo jenisIzinName } kswp name noPkp npwp shippingAddresses { villageAreaCode } status telepon website } institution { institutionType } } } }", "7f5439c12e5801ff6d50e952cfa90820141dea13932691c9ce67a40a1009ca4f": "query meMasterProductCurationDetail($input: MeMasterProductCurationDetailInput!) { meMasterProductCurationDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductCurationDetail { curatorRoleId curatorRoleName description id notes productCurationId rejectReasons { curationType description enum id reason } status } } }", "31e2cce49841d49faf74276378a547fdd8c5ac6e7f9bfc8760b5590eda27658a": "query masterProductCurationRejectReasons($input: MeMasterProductCurationDetailInput!) { masterProductCurationRejectReasons { ... on GenericError { __typename code message reqId } ... on ProductCurationRejectReasonsResponse { __typename items { curationType description enum id reason } } } }", "e0e8fea22128091a15ae1b122f6376de051919579ca6d3f8411b6bb75ee28fc3": "query getListMasterProductV2($input: ListMasterProductInput) { listMasterProductV2(input: $input) { ... on GenericError { __typename code message reqId } ... on ListMasterProductResV2 { currentPage items { access brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber status statusName url } category { id name } createdAt description id images { createdAt deletedAt id imageUrl updatedAt } informations { additionalInformations { createdAt customFormFieldId deletedAt id name updatedAt value } documentInformations { createdAt customFormFieldId deletedAt id name updatedAt value } mainInformations { createdAt customFormFieldId deletedAt id name updatedAt value } } kbki maxPriceUpperBound minPriceLowerBound name options { id option optionLevel values { id sortOrder value } } pdn { countryCode countryName laborDescription laborType locationDescription locationType materialDescription materialType type } priceScheme principal { id name source } regionPrices { id parentRegionCode priceLowerBound priceUpperBound regionCode regionLevel regionName } sellerCompany { bentukUsaha bidangIndustri blacklist { endDate startDate status } companyAddresses { fullAddress id isMainAddress label latitude longitude notes phoneNumber postalCode regionDetail { cityName districtName provinceName villageName } villageAreaCode } id institusiId isCabang isUmkk jenisPerusahaan name npwp picChat picTtd rekanId shippingAddresses { fullAddress id isMainAddress label latitude longitude notes phoneNumber postalCode receiverName regionDetail { cityName districtName provinceName villageName } snapshotId villageAreaCode } skalaUsaha slug status website } shipping { dimensionUnit height length weight weightUnit width } sni { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber status url } status tax { ppnBmId ppnBmPercentage ppnPercentage } tkdn { bmpDate bmpExpirationDate bmpValue brand companyName comparisons { component label productInfo tkdnInfo } date description email expirationDate hs number producerName productType score scoringCategory scoringReason specificationId standard status tkdnBmp type url value } type variants { createdAt deletedAt id isActive optionValues options priceLowerBound priceUpperBound sku sortOrder updatedAt } } lastPage perPage total } } }", "227431454a4f50392f69ebd945a194e84e62fe4b4305c5c2cee0e7e90f8c4bcd": "query negotiationDetail($input: NegotiationDetailInput!) { negotiationDetailV2(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationDetailResponse { __typename latestTotal order { additionalFee { ppn { addOn product shipment shipmentInsurance total } ppnBm } buyerInfo { assignedPPK { buyerSnapshot { buyerId institutionName namaUser nameSatuanKerja unit } } assignedUser { buyerSnapshot { buyerId institutionName namaUser nameSatuanKerja personaRole unit } } } currency expiryTime id items { addOns { addOnVariantId id lastPrice originalPrice qty subTotal tax { ppn ppnBm ppnBmPercentage ppnPercentage } type updatedAt updatedBy } id lastPrice notes originalPrice productId productLabels productVariantId qty snapshot { addOns { id name productAddOnVariants { createdAt description id name price } tax { ppnPercentage ppnTypes } } description id images { imageUrl } name pdn { type } preOrder { sla } prices { productWholesalePrices { minQuantity price } selectedRegionPrice { regionName } } shipping { weight weightUnit } slug stockUnit { primaryUnit } tax { ppnBmPercentage ppnPercentage ppnTypes } type username variants { id optionValues options sku } } subTotal subTotalWithoutTax tax { ppn ppnBm ppnBmPercentage ppnPercentage } } orderKey orderNegotiationIds orderNumber originalTotal paymentStatus payments { id method paymentDetail { paymentId paymentMethodCode } rupDetail { fiscalYear fundingSourceDetails { mak name } fundingSources instituteName locations name procurementMethod procurementTypes rupCode totalBalance workUnitName } } reason sellerInfo { company { address { city } id isUMKK name slug } sellerId } shipmentStatus shipments { awbNumber deliveryOrder { bastNumber destination { address label latitude longitude name phoneNumber } documentMetadata { seller { documentErrorReason } } documentNumber documentUrl estimationTime { maxDay } id pickupTime prices { finalPrice } rateId status volumetric { weight weightUnit } } id lastFee lastInsuranceFee notes originalFee originalInsuranceFee provider requestArrivalDate service shipmentTax { ppnPercentage ppnPercentageInsurance } status subTotal subTotalInsuranceFee } status timestamp updatedAt viaNegotiation viaNegotiation } status } } }", "3c376d0f282eff878b052c54b7d82fc2cbed7cdb41d8901a626fb7d1e821769e": "query negotiationDetailV3($input: NegotiationDetailInput!) { negotiationDetailV3(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationDetailV3Response { __typename expiryTime latestTotal negotiation { paymentSchemeRequest { history { isNewProposal proposerRole } } shipments { shipmentDetail { deliveryOrder { destination { address cityName districtName label latitude longitude name phoneNumber postalCode provinceName villageName } estimationTime { maxDay minDay } id prices { finalPrice } volumetric { weight weightUnit } } id lastFee lastInsuranceFee notes originalFee originalInsuranceFee provider requestArrivalDate requestDateType service shipmentTax { ppn ppnInsuranceFee ppnPercentage ppnPercentageInsurance taxableRate } status } } } order { additionalFee { ppn { addOn product shipment shipmentInsurance total } ppnBm } buyerInfo { assignedPPK { buyerSnapshot { buyerId institutionName kodeSatker namaUser nameSatuanKerja personaRole unit } } assignedUser { buyerSnapshot { buyerId institutionName institutionType kodeSatker namaUser nameSatuanKerja personaRole unit } } } cancellationDetail { type } currency downPayment { createdAt documentId id status updatedAt } expiryTime id isFtz isViaAddendum items { addOns { addOnVariantId id lastPrice originalPrice originalTax { ppn } qty subTotal subTotalWithoutTax tax { ppn ppnBm ppnBmPercentage ppnPercentage taxableRate } type updatedAt updatedBy } id lastPrice notes originalPrice originalTax { ppn ppnBm } productId productLabels productVariantId qty snapshot { addOns { name productAddOnVariants { createdAt description id name price } tax { ppnPercentage ppnTypes taxableRate } type } consolidation { name pricingScheme } description id images { imageUrl } name pdn { type } preOrder { sla } prices { selectedRegionPrice { regionName } } shipping { weight weightUnit } slug stockUnit { primaryUnit } subType tax { ppnBmPercentage ppnPercentage ppnTypes taxableRate } type username variants { id optionValues options sku } } subTotal subTotalWithoutTax tax { ppn ppnBm ppnBmPercentage ppnPercentage taxableRate } } longestPreorderSLA orderKey orderNumber orderTender { contractValue id lelangId lpseId lpseName tenderType } paymentScheme paymentSchemeRequest paymentStatus payments { deliveryOrderGroupId id paymentDetail { orderId orderKey paymentId paymentMethodCode reason status totalAmount } phase rupDetail { fiscalYear fundingSourceDetails { mak name } fundingSources id instituteName kodeSatker locations name procurementMethod procurementTypes rupCode totalBalance workUnitName } status } resellerInfo { company { address { city } isUMKK name slug } sellerId } sellerInfo { company { address { city } id isUMKK name slug } sellerId signer { id isDefault name position } } shipmentStatus shipmentSummary { estimationTime { maxDay minDay } provider service totalShipmentFee totalShipmentInsuranceFee totalShipmentOriginalFee totalShipmentOriginalInsuranceFee totalShipmentOriginalPrice totalShipmentOriginalPriceWithoutPPN totalShipmentOriginalTax { ppn ppnInsuranceFee ppnPercentage ppnPercentageInsurance taxableRate } totalShipmentPrice totalShipmentPriceWithoutPPN totalShipmentTax { ppn ppnInsuranceFee ppnPercentage ppnPercentageInsurance taxableRate } totalWeight totalWeightUnit } status timestamp total totalPayment totalShipment totalWithoutTax type updatedAt useRetention viaNegotiation } orderNegotiationId originalTotal reasons { cancelReason { reason reasonDescription timestamp } rejectReason { reason reasonDescription timestamp } } status } } }", "3c2c426c42c5ac7838c45542f57c6aae0849022d5759b224abdc563e61240cf1": "query negotiationShipmentList($input: NegotiationShipmentListInput!) { negotiationShipmentList(input: $input) { __typename ... on GenericError { __typename code message reqId } ... on NegotiationShipmentListResponse { __typename currentPage items { deliveryOrder { awbNumber bastNumber confirmedAt createdAt deliveryNumber destination { address buyerId cityName districtName label latitude longitude name notes phoneNumber postalCode provinceName villageAreaCode villageName } estimationTime { maxDay maxDayArrival minDay } id logisticName notes orderId origin { cityName name provinceName sellerId } pickupCode pickupTime prices { finalPrice } proofOfDelivery { photo } provider rateId requestDateArrival requestDateArrivalEnd requestDateArrivalNotes requestDateArrivalStart status totalProductPrice totalQtyProduct volumetric { weight weightUnit } } negotiationDeliveryNumber shipmentId subTotalAfterTax } lastPage perPage total } } }", "5b7ddab340c973916a89c47d85755eb718437f39f7da5c088cc80cc5636da8d1": "query negotiationShipmentDetail($input: NegotiationShipmentDetailInput!) { negotiationShipmentDetail(input: $input) { __typename ... on GenericError { __typename code message reqId } ... on NegotiationShipmentDetailResponse { __typename deliveryOrder { logisticName provider } lastFee lastFeeAfterTax productPriceAfterTax shipmentId } } }", "6852d9c1c1f6d3a034c1466cb4ac8f0aacfe6786eacb3607ace66be13a8d523f": "query negotiationTermin($input: NegotiationTerminInput!) { negotiationTermin(input: $input) { __typename ... on GenericError { code message reqId } ... on NegotiationTermin { paymentId totalAddOnPrice totalAddOnQty totalProductPrice totalProductQty totalShipmentPrice totalTerminPrice totalWeight totalWeightUnit } } }", "8182f14ffa1b678bba1b1273c6e5423179d6f49bbd2c6d901a8514329cbbc525": "query orderStatusHistorySummary($id: String!, $sort: SortDirection!) { orderStatusHistorySummary(id: $id, sort: $sort) { ... on GenericError { __typename code message reqId } ... on OrderStatusHistorySummaryResponse { __typename orderStatus { status { ... on AddendumStatusType { __typename addendumStatus: value } ... on NegotiationStatusType { __typename negotiationStatus: value } ... on OrderStatusType { __typename orderStatus: value } } statusActorRole timestamp } } } }", "9afccbaa98ffe5622a59a6c171b4e2f26d1cd6945659d5082a61718d4d543e88": "query getInternalNegotiationList($input: NegotiationListInput!) { negotiationListV2(input: $input) { ... on GenericError { __typename code message reqId } ... on NegotiationListResponse { __typename currentPage items { buyer { buyerSnapshot { institutionName namaUser } } expiryTime firstItem { lastPrice qty snapshot { images { imageUrl } name } tax { ppn ppnBm } } id latestTotal orderId orderKey originalTotal rowItemCount sellerInfo { company { name } } status timestamp } lastPage total } } }", "7efa50bfaa7886d37e5b8aaeb6b6c76eddf86ff2c9fb3b1ac4d0b8750f020a6a": "mutation createProductPrincipal($input: CreateProductPrincipalInput!) { createProductPrincipal(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductPrincipal { applicationNumber approvalAt approvalByPersonaID approvalByUserID brandName brandOwner brandStatus documents { id principalId token } id reason sellerId status type } } }", "2d29591a2aa7e971c1ea6966ea21e4cc4c2403662883118d6f120f392ef45924": "mutation submitProductPrincipalApproval($input: SubmitProductPrincipalApprovalInput!) { submitProductPrincipalApproval(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductPrincipal { applicationNumber approvalAt approvalByPersonaID approvalByUserID brandName brandOwner brandStatus id reason sellerId status type } } }", "fb2eb822c760a083d1cccff7c942a241b34552f5271f26eae7c37fc829860057": "query getListProductPrincipal($input: ListProductPrincipalInput!) { productPrincipalList(input: $input) { ... on GenericError { __typename code message reqId } ... on ListProductPrincipalResponse { currentPage items { applicationNumber approvalAt approvalByPersonaID approvalByUserID brandName brandOwner brandStatus createdAt documents { createdAt id principalId token updatedAt } id reason sellerId status type updatedAt } lastPage perPage total } } }", "007b6c09a0a5b1c72c0fcf30e89e307686e105be712577e00be5333badf17396": "query checkProductPrincipalAvailability($input: ProductPrincipalAvailabilityInput!) { productPrincipalAvailability(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductPrincipalAvailabilityResponse { __typename isAvailable } } }", "70d61ec78429b0b14b9a0f9309af40f23b3a689a5c4551e41c84067b22eec304": "query getProductPrincipalDetail($input: ProductPrincipalDetailInput!) { productPrincipalDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductPrincipal { applicationNumber approvalAt approvalByPersonaID approvalByUserID brandName brandOwner brandStatus createdAt documents { id principalId token } id reason sellerId source status type } } }", "76e6ab1a3264ecf796958f9ce5a4393e98db45af49ac27da27178943e271f0e0": "query getProductBrand($input: ProductBrandInput!) { productBrand(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductBrand { brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber status statusName url } } } }", "238307d076008f2498307c5ee11df00b057045f9b3e6d8db7cecff8d6b5b7223": "query productFileDownload($input: ProductFileDownloadInput!) { productFileDownload(input: $input) { ... on GenericError { code message reqId } ... on ProductFileDownloadResponse { files { signedUrl token } type } } }", "cea31181146697da51825440b2ff6bb1f21605cf165a5a0088c6dc63b13360b7": "query getPrincipalApplicantCompany($id: String!) { getInstitutionByID(id: $id) { ... on GenericError { __typename code message reqId } ... on Institution { __typename company { bentukUsaha bidangIndustri id name nib npwp status } institution { institutionType } } } }", "64a9fe678733595275d6a183e61924fa4b2ab8f29dbdb3b049068e55c7ec92ca": "query getPrincipalApprovalPersona($filter: PersonaFilterInput!, $pagination: AccountPagination!) { searchPersona(filter: $filter, pagination: $pagination) { ... on GenericError { __typename code message reqId } ... on SearchPersonaRes { __typename users { name } } } }", "68f7b197450455d6c396de5b6b2a5cecd5bdbb3945adcfb0721f4fbbefbfe835": "mutation updateProductReview($id: ID!, $input: ProductReviewInput!) { updateProductReview(id: $id, input: $input) { ... on GenericError { __typename code message reqId } ... on StatusResponse { __typename data { status } } } }", "f4453b6f36d789157d33d1ded13e9df81a46cadb0b56e41c6f344b0e96e2fbe4": "mutation submitProductCurationDetail($input: SubmitCurationDetailInput!) { submitProductCurationDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on SubmitProductCurationDetailResponse { __typename productCurationDetail { curatorRoleId curatorRoleName description id notes productCurationId rejectReasons { curationType description enum id reason } status } productStatus } } }", "f343ae690a35de552f51c08a018e1c549d88def973dbeb20fb1f70152cbdaa7c": "query getProductByID($id: ID!) { getProduct(id: $id) { ... on GenericError { __typename code message reqId } ... on Product { actionReasons { description enum reason } actionReasonsNotes brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber status statusName url } category { id isActive name } categoryType construction { code descriptions id isSmkk isUmum jobType materials { code coefficient formulaCoefficient id inputCoefficient name orderNumber price unit } name referenceType tools { code coefficient formulaCoefficient id inputCoefficient name orderNumber price unit } unit workerPackages { code coefficient formulaCoefficient id inputCoefficient name orderNumber price unit } } curation { details { curatorRoleId curatorRoleName description id notes productCurationId rejectReasons { curationType description enum id reason } status } id productId status type } curationType description id images { id imageUrl } isActive isSellerUMKK kbki kfa { isConsolidation isJkn packageKfaCode } labels masterProductId name pdn { laborType locationType materialType type } preOrder { sla } prices { maxPrice maxPriceWithTax maxTaxablePrice minPrice minPriceWithTax minPurchase minTaxablePrice productWholesalePrices { id minQuantity price priceWithTax taxablePrice } selectedRegionPrice { id parentRegionCode price regionCode regionLevel regionName taxablePrice } } productAddOns { description id name productAddOnVariants { description id name price priceWithTax taxablePrice } tax { ppnPercentage ppnTypes taxableRate } type } productInformations { additionalInformations { customFormFieldId id name value } documents { customFormFieldId id name value } mainInformations { customFormFieldId id name value } } productOptions { id option optionLevel valueIds values } sellerId shipping { dimensionUnit hasSellerShipping height length weight weightUnit width } slug sni { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber status url } status stockUnit { primaryUnit secondaryUnit value } subType tax { ppnBmId ppnBmPercentage ppnBmTaxPaymentFileToken ppnBmTaxPaymentStatus ppnBmType ppnPercentage ppnTypes taxableRate } tkdn { bmpDate bmpExpirationDate bmpValue brand companyName date description email expirationDate hs number producerName productType score scoringReason specificationId standard status type url value } type unitSold updatedAt username variants { id imageUrl isActive optionValues options price priceWithTax priceWithTax sku sortOrder stock taxablePrice } videoSource videoUrl } } }", "a10a016bb1d3c1f6aca630d14d624c74813eafceee7784ef634157d9bd5c5e15": "query getProductByIdDevelopment($id: ID!) { getProduct(id: $id) { ... on GenericError { __typename code message reqId } ... on Product { actionReasons { description enum reason } actionReasonsNotes brand { applicationNumber brandName expirationDate fillingDate ownerName registrationNumber status statusName url } category { id isActive name } categoryType construction { code descriptions id isSmkk isUmum jobType materials { code coefficient formulaCoefficient id inputCoefficient name orderNumber price unit } name referenceType tools { code coefficient formulaCoefficient id inputCoefficient name orderNumber price unit } unit workerPackages { code coefficient formulaCoefficient id inputCoefficient name orderNumber price unit } } curation { details { curatorRoleId curatorRoleName description id notes productCurationId rejectReasons { curationType description enum id reason } status } id productId status type } curationType description id images { id imageUrl } isActive isSellerUMKK kbki kfa { isConsolidation isJkn packageKfaCode } labels masterProductId name pdn { laborType locationType materialType type } preOrder { sla } prices { maxPrice maxPriceWithTax maxTaxablePrice minPrice minPriceWithTax minPurchase minTaxablePrice productWholesalePrices { id minQuantity price priceWithTax taxablePrice } selectedRegionPrice { id parentRegionCode price regionCode regionLevel regionName taxablePrice } } productAddOns { description id name productAddOnVariants { description id name price priceWithTax taxablePrice } tax { ppnPercentage ppnTypes taxableRate } type } productInformations { additionalInformations { customFormFieldId id name value } documents { customFormFieldId id name value } mainInformations { customFormFieldId id name value } } productOptions { id option optionLevel valueIds values } sellerId shipping { dimensionUnit hasSellerShipping height length weight weightUnit width } slug sni { accreditationScheme brand certificateNumber certificationScheme companyName expirationDate productName publishedDate sniNumber spptNumber status url } status stockUnit { primaryUnit secondaryUnit value } subType tax { ppnBmId ppnBmPercentage ppnBmTaxPaymentFileToken ppnBmTaxPaymentStatus ppnBmType ppnPercentage ppnTypes taxableRate } tkdn { bmpDate bmpExpirationDate bmpValue brand companyName date description email expirationDate hs number producerName productType score scoringReason specificationId standard status type url value } type unitSold updatedAt username variants { id imageUrl isActive optionValues options price priceWithTax priceWithTax sku sortOrder stock taxablePrice } videoSource videoUrl } } }", "a4af03ba3e8152926a6888abcaa37fbaafb80810fa3c5063e97791b269027bca": "query getInfoPenyediaByID($id: String!) { getInstitutionByID(id: $id) { ... on GenericError { __typename code message reqId } ... on Institution { __typename company { bentukUsaha companyAddresses { fullAddress isMainAddress label notes phoneNumber postalCode villageAreaCode } id isCabang isUmkk izin { isVerify izinUsahaExpired izinUsahaInstitusi izinUsahaNo jenisIzinName } kswp name noPkp npwp shippingAddresses { villageAreaCode } status telepon website } institution { institutionType } } } }", "6d5d064d46572186378d6c8e1196fb884273831752464c80499a6688671d40b1": "query getTraceParentRegion($areaCodes: [String!]) { getTraceParentRegion(query: {areaCodes: $areaCodes}) { items { cityAreaCode cityId cityName districtAreaCode districtId districtName provinceAreaCode provinceId provinceName villageAreaCode villageId villageName villagePostalCode } } }", "d2af6b8b8bd6cf74ab29f6a957762cf6adea0a759a680281c63abc265f84af09": "query getActiveLogisticConfiguration($productId: ID!) { activeLogisticConfiguration(productId: $productId) { ... on GenericError { __typename code message reqId } ... on LogisticConfigurationResponse { __typename data { code id logoUrl name rates { id isActive isShown name type } } } } }", "2aa319795e22ecc61d6c351632aa46dd7a62cc5cfc0f4980f37366c1732cea69": "query getSpecialProductRegionPrice($productId: ID!) { specialProductRegionPrice(productId: $productId) { ... on GenericError { __typename code message reqId } ... on ProductRegionPrices { __typename items { id price priceWithTax regionCode regionLevel regionName taxablePrice } } } }", "61c89620a7923a9fc6a1267554eec5eb8fe97bfe18bcfa2a1e3d944ae1011ce7": "query getSpecialProductRegionPriceDevelopment($productId: ID!) { specialProductRegionPrice(productId: $productId) { ... on GenericError { __typename code message reqId } ... on ProductRegionPrices { __typename items { id price priceWithTax regionCode regionLevel regionName taxablePrice } } } }", "20f746bdd6806eb3a01474170e69616379e644238d0476f6f793243b6531cabd": "query getProductCategoryAllLevel($id: ID!) { getProductCategory(id: $id) { ... on GenericError { __typename code message reqId } ... on ProductCategory { id name parent { id name parent { id name } } } } }", "223064d838d9bad43f601f01940bb67320112fd15ebff8a5f4f5a4d0a67c1706": "query activeRegionSellerCourier($parentRegionCode: String, $productId: String, $regionLevel: String!, $sellerId: String) { activeRegionSellerCourier( regionLevel: $regionLevel productId: $productId sellerId: $sellerId parentRegionCode: $parentRegionCode ) { ... on ActiveRegionPriceResponse { data { childLevelState pricePerKg regionCode regionInfo { name } } } ... on GenericError { __typename code message reqId } } }", "9e599569324a6119959e056523e19a4f51e0344a39f472de51be3bfa3ed80a1d": "query getProductCategoryById($id: ID!) { getProductCategory(id: $id) { ... on GenericError { __typename code message reqId } ... on ProductCategory { additionalInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } additionalInfoCustomFormId allowedPpnPercentage createdAt curationEnabled deletedAt documents { customFormId id mandatory name order showOnLogin type } hasVat hetPriceEnabled icon id isActive kbki { classCodes commodityCodes commodityGroupCodes divisionCodes groupCodes sectionCodes subclassCodes } kbli level masterProductEnabled name nieEnabled parentId ppnPercentage primaryInfo { additionalInfo customFormId defaultValue id mandatory name order showOnLogin type } primaryInfoCustomFormId productType slug subcategoriesCount type updatedAt zoningPriceEnabled } } }", "b163ca4748e10760f9423c5a7a70947eb16ab37d2886bb11dd5e754e2624f2c4": "query meProductCurationDetail($input: MeProductCurationDetailInput!) { meProductCurationDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductCurationDetail { curatorRoleId curatorRoleName description id productCurationId rejectReasons { curationType description enum id reason } status } } }", "7cfe8ac68a28bd82d270a9e7f5480c269d1057ae655bc34fc0e3254b7135e618": "query getProductCurationRejectReasons($input: ProductCurationRejectReasonsInput!) { productCurationRejectReasons(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductCurationRejectReasonsResponse { __typename items { curationType description enum id reason } } } }", "5c9fad2890d574ba4c1ae9fce697fc21bff3ae932416f5811468f075407e0e5d": "query principalApplicationDocuments($input: ProductPrincipalDetailInput!) { productPrincipalDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductPrincipal { documents { id principalId token } } } }", "64b1a414bbac71d054af156ad0954720b5ef4fd2358c2196bc40765dcaf9f40e": "mutation createProductBulkAction($input: CreateProductBulkActionInput!) { createProductBulkAction(input: $input) { ... on CreateProductBulkActionResponse { id } ... on GenericError { __typename code message reqId } } }", "5df9699330618bbf0b0648a619b2f7bc57e308983cd4892a4063344160d9b416": "mutation publishProductBulkAction($input: PublishProductBulkActionInput!) { publishProductBulkAction(input: $input) { ... on GenericError { __typename code message reqId } ... on StatusResponse { __typename data { status } } } }", "7ae44d99efc62af3bbe87b6fcf2a3cd1a10cb63de7c9b67a74a56b9fd8ad6a89": "query getProductBulkActionTemplate($input: ProductBulkActionTemplateInput!) { productBulkActionTemplate(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductBulkActionTemplateResponse { url } } }", "b58a3077220a63bb60116c77100e33cd5e83460408f22ed12ede15eee95d7d59": "query getListProductBulkActionDetailItem($input: ListProductBulkActionDetailItemInput!) { listProductBulkActionDetailItem(input: $input) { ... on GenericError { __typename code message reqId } ... on ListProductBulkActionDetailItemResponse { currentPage items { description product { category { id isActive name } categoryType createdAt hasVat id images { createdAt id imageUrl updatedAt } isActive isSellerUMKK kbki labels name pdn { countryCode countryName laborDescription laborType locationDescription locationType materialDescription materialType type } prices { maxPrice maxPriceWithTax minPrice minPriceWithTax minPurchase productWholesalePrices { createdAt id minQuantity price priceWithTax updatedAt } selectedRegionPrice { id parentRegionCode price priceWithTax regionCode regionLevel regionName } } productAddOns { createdAt description id name objectType productAddOnVariants { createdAt description id name price priceWithTax updatedAt } tax { ppnPercentage ppnTypes } type updatedAt } productInformations { additionalInformations { createdAt customFormFieldId id name updatedAt value } documents { createdAt customFormFieldId id name updatedAt value } mainInformations { createdAt customFormFieldId id name updatedAt value } } productOptions { id option optionLevel valueIds values } sellerId sellerLocation { cityName districtName isFTZ paramId } sellerName sellerVillageAreaCode shipping { dimensionUnit hasSellerShipping height length weight weightUnit width } shippingOption slug status stockAccumulation tax { ppnBmId ppnBmPercentage ppnBmType ppnPercentage ppnTypes } tkdn { bmpValue companyName email number producerName specificationId tkdnBmp url value } type unitSold updatedAt username variants { createdAt id imageUrl isActive optionValues options price priceWithTax sellerId sku sortOrder stock updatedAt } version } reason } lastPage perPage total } } }", "9bc1ee4bf2fb87b7fe0c2858248c4de3ae2e365453dc4c7c0c6443d17aa052e1": "query listProductBulkAction($input: ListProductBulkActionInput!) { listProductBulkAction(input: $input) { ... on GenericError { __typename code message reqId } ... on ListProductBulkActionResponse { currentPage items { completedAt createdAt createdBy downloadUrl failedCount fileName id rowCount startedAt status successCount type } lastPage perPage total } } }", "3db4ee1d96a8275b5fb811137581a0a2937bbcbc726916f0bc6090aaacd97a8d": "query searchProducts($input: SearchProductInput!) { searchProducts(input: $input) { ... on GenericError { __typename code message reqId } ... on ListSearchProductResponse { aggregations { ... on CategoryAllLevelAggregations { __typename categories { children { children { count id name } count id name } count id name } } } currentPage items { category { name } createdAt defaultPrice defaultPriceWithTax id images isActive isPreOrder isRegionPrice isSellerUMKK isSellerUMKK isWholesale labels location { name regionCode } maxPrice maxPrice maxPriceWithTax minPrice minPrice minPriceWithTax name score scoreDetail { keywordScore locationScore priceScore ratingScore tkdnScore umkkScore unitSoldScore } sellerId sellerName slug status stockAccumulation stockAvailability tkdn { bmpValue tkdnBmp value } type unitSold username } lastPage perPage total } } }", "7888ac7d38fe7032cfa05cd936f38674d417c0fd96e1dacf0799caef07df5acf": "query getAllMinifiedCategory($input: AllMinifiedProductCategoryInput!) { allMinifiedProductCategory(input: $input) { ... on AllMinifiedProductCategories { __typename items { children { children { id level name parentId } id level name parentId } id level name parentId } } ... on GenericError { __typename code message reqId } } }", "3e49983d2a8277b1a40937c843c3472ab16d8c8781a7f2458886c9201c3a2685": "query getAllProductCategoryList($input: ListCategoryInput) { getAllProductCategory(input: $input) { ... on GenericError { __typename code message reqId } ... on ProductCategories { __typename currentPage items { allowedPpnPercentage curationEnabled hasVat id isActive kbli name ppnPercentage productType type } lastPage perPage total } } }", "4fa6b0405777c90e2c106e971ffd2c7c118d73f0ddb33a9d0d8042a31ed17aed": "mutation UploadImageWithPublicKey($input: OrderRequestUploadSignedUrlInput!) { orderRequestSignedUrl(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderRequestUploadSignedUrlResponse { __typename expiry identifier publicKey signedUrl token } } }", "a5b85e84cb6aa3b06c65855c89eabea190fadbf875dfd57f379b734b026ccc8c": "mutation RequestCancel($input: AdminRequestCancelInput!) { orderRequestCancelAdmin(input: $input) { ... on AdminRequestCancelResponse { __typename id } ... on GenericError { __typename code message reqId } } }", "59650a1f7c3ca9f9861a567b332074d85577965b827b98ffba481e1e55cfadcc": "mutation submitRequestCancel($input: ApprovalInput!) { orderRequestCancelAdminApproval(input: $input) { ... on ApprovalResponse { id } ... on GenericError { __typename code message reqId } } }", "0c78e049d1c00314eb5421731e79e769c75351dbaf78b621992f9b0b60dc110b": "mutation retryPayment($paymentId: ID!) { retryPayment(paymentID: $paymentId) { ... on GenericError { __typename code message reqId } ... on RetryPaymentRes { __typename paymentId } } }", "66d7c8488f8d0c38f98c5cf293da8afc1131157b362994c94790fc742b827dfc": "mutation paymentOutsideSystem($paymentId: ID!) { paymentOutsideSystem(paymentID: $paymentId) { ... on GenericError { __typename code message reqId } ... on PaymentOutsideSystemRes { __typename paymentId } } }", "1949f0d6d874e459554511633c7e35f3953c5336a81edb21b41feb996957812f": "query orderDetailV2($input: OrderInput!) { orderDetailV2(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderV2 { __typename addenda { clauses id newTotal orderItems { orderItem { addOns { id qty subTotal tax { ppn ppnPercentage taxableRate } } id qty subTotal tax { ppn ppnBm ppnBmPercentage ppnPercentage taxableRate } } } proposerRole shipments { shipment { lastFee lastInsuranceFee shipmentTax { ppn ppnPercentage ppnPercentageInsurance taxableRate } } } status } buyerInfo { assignedPPK { buyerSnapshot { institutionName kodeSatker namaUser nameSatuanKerja unit } } assignedUser { buyerSnapshot { institutionName institutionType kodeSatker namaUser nameSatuanKerja personaRole unit } } } cancellationDetail { chargedParties chargedPartiesDetail escalationDetail picInfo { name } reason reasonCategory rejectedAt timestamp type type } checkoutSource competitionInfo { id key } expiryTime id isViaAddendum items { addOns { addOnVariantId id lastPrice originalPrice originalTax { ppn } qty subTotal tax { ppn ppnPercentage taxableRate } } id lastPrice notes originalPrice originalTax { ppn ppnBm } productId productLabels productVariantId qty snapshot { addOns { name productAddOnVariants { id name } } id images { imageUrl } name pdn { type } preOrder { sla } prices { selectedRegionPrice { regionName } } shipping { weight weightUnit } stockUnit { primaryUnit } subType type variants { optionValues } } subTotal subTotalWithoutTax tax { ppn ppnBm ppnBmPercentage ppnPercentage taxableRate } } longestPreorderSLA orderKey orderNegotiationIds orderNumber orderTender { contractValue lelangId lpseName tenderType } paymentScheme paymentSchemeRequest payments { deliveryOrderGroupId id paymentDetail { paymentId status } phase rupDetail { fiscalYear fundingSourceDetails { mak name } fundingSources id instituteName kodeSatker locations name procurementMethod procurementTypes rupCode totalBalance workUnitName } status } resellerInfo { company { address { city } isUMKK name slug } sellerId } sellerInfo { company { address { city } isUMKK name slug } sellerId } shipmentSummary { estimationTime { maxDay minDay } provider service totalShipmentFee totalShipmentInsuranceFee totalShipmentOriginalPrice totalShipmentOriginalPriceWithoutPPN totalShipmentOriginalTax { ppn ppnPercentage taxableRate } totalShipmentPrice totalShipmentPrice totalShipmentPriceWithoutPPN totalShipmentTax { ppn ppnPercentage ppnPercentageInsurance taxableRate } totalWeight totalWeightUnit } status timestamp total totalPayment totalShipment totalWithoutTax type updatedAt viaNegotiation workingProgresses { id notes paymentId status } } } }", "a2208747660894896bf3a770fda2e22b301fedc87d56d9feeb8afe6db70ddc00": "query getProductInformationByIds($input: BulkProductInput!) { products(input: $input) { ... on GenericError { __typename code message reqId } ... on Products { __typename items { actionReasons { description enum reason } id status } } } }", "149c470d5abc7cf9f74900076afafdeb14488750f5997a5a4bd85a554ebbdfb2": "query orderShipmentItems($deliveryOrderId: String!) { orderShipmentItems(deliveryOrderId: $deliveryOrderId) { ... on GenericError { __typename code message reqId } ... on OrderShipmentItemResponse { __typename shipmentItems { id orderItem { id notes snapshot { addOns { name } name stockUnit { primaryUnit } variants { id optionValues options } } } qty } } } }", "f105b043e0688cca22489839c1c24c94519720b1b174956b673682c90510e9eb": "query orderTerminSummaryDev($paymentId: String!) { orderTermin(paymentId: $paymentId) { ... on GenericError { __typename code message reqId } ... on OrderTermin { id payment { billAmount disbursementDeductions { amount deductionCode deductionName } metadata { institutionCategory isCoaInputted isRupLocked } paymentId paymentMethodCode reason status totalAmount } rupDetail { fiscalYear fundingSourceDetails { mak name } fundingSources id instituteName kodeSatker locations name procurementMethod procurementTypes rupCode totalBalance workUnitName } status totalAddOnBasePrice totalAddOnPrice totalAddOnQty totalProductBasePrice totalProductPrice totalProductQty totalShipmentBasePrice totalShipmentPrice totalTerminPpn totalTerminPpnBm totalTerminPrice totalWeight totalWeightUnit } } }", "52fd1b816fd74c269f90ebb471638b87c2d5d8ed810634ccaa50438b5648abcd": "query orderTerminSummary($paymentId: String!) { orderTermin(paymentId: $paymentId) { ... on GenericError { __typename code message reqId } ... on OrderTermin { id payment { billAmount disbursementDeductions { amount deductionCode deductionName } metadata { institutionCategory isCoaInputted isRupLocked } paymentId paymentMethodCode reason status totalAmount } rupDetail { fiscalYear fundingSourceDetails { mak name } fundingSources id instituteName kodeSatker locations name procurementMethod procurementTypes rupCode totalBalance workUnitName } status totalAddOnPrice totalAddOnQty totalProductPrice totalProductQty totalShipmentPrice totalTerminPrice totalWeight totalWeightUnit } } }", "e5b79ddef38a1b6f2de33e482d7fbddaecbb5a82d8badac912579e452028af65": "query deliveryOrderList($filter: FilterDeliveryOrderList!, $pagination: Pagination!, $sort: DeliveryOrderListSort) { deliveryOrderList( input: {filter: $filter, pagination: $pagination, sort: $sort} ) { ... on DeliveryOrderList { __typename currentPage deliveryOrders { awbNumber bastNumber confirmedAt createdAt deliveryNumber destination { address buyerId cityName districtName label latitude longitude name notes phoneNumber postalCode provinceName villageAreaCode villageName } estimationTime { maxDay maxDayArrival minDay } id logisticName notes orderId origin { cityName name provinceName sellerId } pickupCode pickupTime prices { finalPrice } proofOfDelivery { photo } rateId requestDateArrival requestDateArrivalEnd requestDateArrivalNotes requestDateArrivalStart status totalProductPrice totalQtyProduct volumetric { weight weightUnit } } lastPage perPage totalItem } ... on GenericError { __typename code message reqId } } }", "f7b6a436466670085fc1b31a7ce40e6383ff3d72afbf66a5af684054fc517f4f": "query deliveryOrderListDev($filter: FilterDeliveryOrderList!, $pagination: Pagination!, $sort: DeliveryOrderListSort) { deliveryOrderList( input: {filter: $filter, pagination: $pagination, sort: $sort} ) { ... on DeliveryOrderList { __typename currentPage deliveryOrders { awbNumber bastNumber confirmedAt createdAt deliveryNumber destination { address buyerId cityName districtName label latitude longitude name notes phoneNumber postalCode provinceName villageAreaCode villageName } detailPrice { shipmentFeeWithoutPPN } estimationTime { maxDay maxDayArrival minDay } id logisticName notes orderId origin { cityName name provinceName sellerId } pickupCode pickupTime prices { finalPrice } proofOfDelivery { photo } rateId requestDateArrival requestDateArrivalEnd requestDateArrivalNotes requestDateArrivalStart status totalProductPrice totalProductPriceWithoutPPN totalQtyProduct volumetric { weight weightUnit } } lastPage perPage totalItem } ... on GenericError { __typename code message reqId } } }", "c2ffce92b4e824612cf8ce2adca797b2dac7757854fc1630b8d71166301de766": "query getDeliveryOrderStatusHistory($deliveryOrderId: ID!) { deliveryOrderStatusHistory(deliveryOrderId: $deliveryOrderId) { ... on DeliveryOrderStatusHistoryResponse { __typename data { detail { driverName pickupTime vehicleNumber } trackings { createdAt deliveryOrderId externalStatusCode externalStatusDescription externalStatusName id providerStatusCode providerStatusDescription providerStatusName status statusCreatedAt statusDescription } } } ... on GenericError { __typename code message reqId } } }", "ae375f7a244f1bcfbd82480e1820c5582b2a9f678cab2c07b68222e1bd5488ca": "query orderDocuments($orderId: String!) { orderDocuments(orderId: $orderId) { ... on GenericError { __typename code message reqId } ... on OrderDocumentsResponse { __typename letters { buyerUrl category createdAt documentTokenProvider errorReason fileType id name status } paymentLetters { letters { buyerUrl category createdAt documentReason fileType id name updatedAt } phase shipmentLetters { letters { buyerUrl category createdAt documentReason fileType id name status updatedAt } } status } } } }", "1d5b29db017aa868cd7218c170c0beb5fc04269fba59f232c5351a6ba5284a7c": "query orderActionButton($orderId: String!) { orderActionButton(orderId: $orderId) { ... on GenericError { __typename code message reqId } ... on OrderActionButtonResponse { __typename order } } }", "7020fe3ca714b1a5885c8828ea39596335e199c4830567c3936ce50ce3bf0f20": "query orderDetailV2ForShipmentSummary($input: OrderInput!) { orderDetailV2(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderV2 { id payments { deliveryOrderGroupId id phase } } } }", "4523f37bd33162983f0044a860859ee10cb2cd7890b0c08377557dc6bb193eb9": "query deliveryOrderGroup($groupId: ID!) { deliveryOrderGroup(groupId: $groupId) { ... on DeliveryOrderGroup { deliveryOrders { deliveryNumber destination { address label name phoneNumber } id totalQtyProduct } id } ... on GenericError { __typename code message reqId } } }", "54b67bc99165c18607ea4a26824cafb5e8fd61aa14f7894f4e4f7027ecb8432e": "query getOrderWorkingProgressesV1($workingProgressId: String!) { orderWorkingProgress(workingProgressId: $workingProgressId) { ... on GenericError { __typename code message reqId } ... on WorkingProgress { createdAt id isLastWorkingProgress items { addOns { addOnName amount amountWithTax variantName } amount amountWithTax detailProgress maxAmount orderItemId productId productName variantName } paymentId status totalAmount totalAmountWithTax updatedAt } } }", "376969456ca2bd351a0b399d50197908867b51c775e7728fc87349dfbda9435c": "query getOrderWorkingProgresses($workingProgressId: String!) { orderWorkingProgress(workingProgressId: $workingProgressId) { ... on GenericError { __typename code message reqId } ... on WorkingProgress { confirmedAt createdAt id isLastWorkingProgress items { addOns { addOnName amount amountWithTax variantName } amount amountWithTax detailProgress maxAmount orderItemId productId productName variantName } paymentId status totalAmount totalAmountWithTax totalPpn totalPpnBm updatedAt } } }", "22db633d5f390fc832851060aad0ddd7c4e33b64b607d7332c70cf75b19a0f6b": "query orderDetailV2ForWorkingProgressSummary($input: OrderInput!) { orderDetailV2(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderV2 { id orderKey workingProgresses { id } } } }", "8d9576d07dbcdf36936d88cae14e7445b93e06375d82151548bf9acff939c74a": "query getOrderWorkingProgressesSummary($workingProgressId: String!) { orderWorkingProgress(workingProgressId: $workingProgressId) { ... on GenericError { __typename code message reqId } ... on WorkingProgress { id items { addOns { addOnName amountWithTax variantName } amountWithTax detailProgress orderItemId productName variantName } totalAmountWithTax } } }", "f14d3e66474fe74ef82b4237c29dd751947ef715b8b0e2de95167a990c33245b": "query getCoaAvailability($paymentID: ID!) { paymentCOAAvailability(paymentID: $paymentID) { ... on COAList { __typename items { coa16seg itemDesc itemId sisaPagu subKompDesc subKompId } } ... on GenericError { __typename code message reqId } } }", "cc1fcad56eb308e2c51e459dda8368ecbaf7ce6796479cf785dd7cce72181b9f": "query getInternalOrderListMultishipment($input: OrderListInput!) { orderListV2(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderListResponse { __typename currentPage items { buyer { buyerSnapshot { institutionName namaUser } } cancellationDetail { type } checkoutSource expiryTime firstItem { lastPrice qty snapshot { images { imageUrl } name stockUnit { primaryUnit } } tax { ppn ppnBm } tax { ppn ppnBm } } id isViaAddendum orderKey orderNumber paymentStatus payments { method } resellerInfo { company { isUMKK name } } rowItemCount sellerInfo { company { isUMKK name } } shipments { service } status timestamp total type updatedAt viaNegotiation } lastPage total } } }", "3ae3b149617852e1be83996584e5df2186a296ebe1e0988133b199b3f189ce3d": "mutation UploadImage($input: OrderRequestUploadSignedUrlInput!) { orderRequestSignedUrl(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderRequestUploadSignedUrlResponse { __typename expiry identifier signedUrl token } } }", "867faeaa2a0e2c98be0c9ece363e7d6a3299873deff2aae097d0b9405839b0df": "query getOrderDetail($input: OrderInput!) { orderDetail(input: $input) { ... on GenericError { __typename code message reqId } ... on Order { __typename addenda { additionalFee { ppn { addOn product shipment shipmentInsurance total } ppnBm } histories { createdAt notes status } id newTotal newTotalWithoutTax orderItems { orderItem { addOns { id qty subTotal subTotalWithoutTax tax { ppnBmPercentage ppnPercentage } } id productId qty snapshot { stockUnit { primaryUnit } tax { ppnBmPercentage ppnPercentage ppnTypes } } subTotal subTotalWithoutTax tax { ppn ppnBm ppnBmPercentage ppnPercentage } } } proposerRole reason shipments { shipment { lastFee lastInsuranceFee requestArrivalDate shipmentTax { ppnPercentage ppnPercentageInsurance } } } status } additionalFee { ppn { addOn product shipment shipmentInsurance total } ppnBm } buyerInfo { assignedPPK { buyerSnapshot { buyerId institutionName namaUser nameSatuanKerja unit } } assignedUser { buyerSnapshot { buyerId institutionName namaUser nameSatuanKerja personaRole unit } } } cancellationDetail { chargedParties chargedPartiesDetail escalationDetail picInfo { name } reason reasonCategory timestamp type } currency documents { buyerUrl category id name sellerUrl } expiryTime id isCOAFilled items { addOns { addOnVariantId id lastPrice originalPrice qty subTotal tax { ppn ppnBm ppnBmPercentage ppnPercentage } type updatedAt updatedBy } id lastPrice notes originalPrice productId productId productLabels productVariantId qty snapshot { addOns { name productAddOnVariants { createdAt description id name price } tax { ppnPercentage ppnTypes } } description id images { imageUrl } name pdn { type } preOrder { sla } prices { productWholesalePrices { minQuantity price } selectedRegionPrice { regionName } } shipping { weight weightUnit } slug stockUnit { primaryUnit } tax { ppnBmPercentage ppnPercentage ppnTypes } type username variants { id optionValues options sku } } subTotal subTotalWithoutTax tax { ppn ppnBm ppnBmPercentage ppnPercentage } } orderKey orderNegotiationIds orderNumber originalTotal paymentStatus payments { id method paymentDetail { disbursementDeductions { amount deductionCode deductionName } documents { documentFileName documentNumber documentType documentURL id updatedAt } metadata { institutionCategory isCoaInputted } paymentId paymentMethodCode totalDisbursement } rupDetail { fiscalYear fundingSourceDetails { mak name } fundingSources instituteName locations name procurementMethod procurementTypes rupCode totalBalance workUnitName } } reason sellerInfo { company { address { city } id isUMKK name slug } sellerId } shipmentStatus shipments { awbNumber deliveryOrder { bastNumber destination { address label latitude longitude name phoneNumber } documentMetadata { seller { documentErrorReason } } documentNumber documentUrl estimationTime { maxDay } id pickupTime prices { finalPrice } rateId status volumetric { weight weightUnit } } id lastFee lastInsuranceFee notes originalFee originalInsuranceFee provider requestArrivalDate service shipmentTax { ppnPercentage ppnPercentageInsurance } status subTotal subTotalInsuranceFee } status timestamp total updatedAt viaNegotiation } } }", "ed0ab20d6d0452499e47a9cee6cc4e371537da13ec0d03da4d4871ac475c9eae": "query getDeliveryOrder($orderId: ID!) { deliveryOrder(orderId: $orderId) { ... on DeliveryOrder { awbNumber createdAt destination { address buyerId cityName districtName latitude longitude name phoneNumber postalCode provinceName villageName } documentUrl estimationTime { maxDay minDay } externalId id isInsurance orderId origin { cityName name provinceName sellerId } pickupCode pickupTime prices { basePrice discount finalPrice insuranceFee surchargeFee } proofOfDelivery { photo signature } rateId requestDateArrival shippingLabelId status updatedAt volumetric { dimensionUnit height length weight weightUnit width } } ... on GenericError { __typename code message reqId } } }", "d12ff67a7cf2265441bbf6b3bc191782bac7ec5282e2caa1806c8656b4484b53": "query getLogisticRateData($logisticRateId: ID!) { logisticRate(id: $logisticRateId) { ... on GenericError { __typename code message reqId } ... on RateData { id logistic { code id logoUrl name } maxWeight minWeight name provider providerRateId type volumetricFactor } } }", "c2ba94112341ccf7e614191d7ea63883fba9a213c0ecd500022052c01f68297f": "query getInternalReviewPPKList($input: OrderListInput!) { orderListV2(input: $input) { ... on GenericError { __typename code message reqId } ... on OrderListResponse { __typename currentPage items { buyer { buyerSnapshot { institutionName namaUser } } expiryTime firstItem { lastPrice qty snapshot { images { imageUrl } name } tax { ppn ppnBm } tax { ppn ppnBm } } id orderKey paymentStatus payments { method } rowItemCount sellerInfo { company { name } } status timestamp total updatedAt } lastPage total } } }", "dfbaae1318377342d64f9b956a25c7311d88c0a910fa08ee9157434a6ed9ee8a": "query getUserInfo { me { ... on GenericError { __typename message reqId } ... on UserInfo { __typename email id isSeller name phone type username } } }", "6cb1459d7eb010d3f4dc008b607f0d96486311d168f47091da84184dccd37b0a": "query getCompanyPublicBlacklist($filter: CompanyPublicFilter!) { companyPublic(filter: $filter) { ... on CompanyPublicResult { __typename companies { blacklist { endDate startDate status } oss { project { kbli { id kbli name } } } } } ... on GenericError { __typename code message reqId } } }", "10dc0679260a211df6990a2b033f75d2109aa22abc8c367b8549dd66fe674c03": "query requestUploadSignedUrl($input: RequestUploadSignedUrlInput!) { requestUploadSignedUrl(input: $input) { ... on GenericError { __typename code message reqId } ... on RequestUploadSignedUrlRes { __typename expiry identifier jwtToken signedUrl } } }", "789ed4a94219c67f63b339c8b3462efc907e3cdd228cfa9f638137ed5765ad9d": "query satker($filter: SatkerFilter, $pagination: AccountPagination!) { satker(pagination: $pagination, filter: $filter) { ... on GenericError { __typename code message reqId } ... on SatkerResult { __typename items { alamat kodeKlpd kodeSatker namaKlpd namaSatker npwp } paginationInfo { currentPage total } } } }"}