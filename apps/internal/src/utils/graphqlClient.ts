import {
  type RequestMiddleware,
  type ResponseMiddleware,
} from "graphql-request/build/esm/types"
import {
  type TRequest,
  checkUnauthorized,
  graphqlClientMiddlewareV2,
  PersistedQueryGraphQLClient,
} from "shared-utils"

import { signIn, signOut } from "@/authentication/authService"
import { IS_DEVELOPMENT_OR_STAGING } from "@/commons/flags"
import { GRAPHQL_URL } from "@/config/api"
import { AUTH } from "@/config/client"

const requestMiddleware: RequestMiddleware = (request) => {
  return graphqlClientMiddlewareV2({
    request: request as TRequest,
    signOut,
    app: "INTERNAL",
    baseUrl: AUTH.BASE_URL,
  })
}

export const responseMiddleware: ResponseMiddleware = (response) => {
  if (checkUnauthorized(response)) {
    window && signIn("refreshToken")
  }
}

export const federatedGqlClient = new PersistedQueryGraphQLClient(
  GRAPHQL_URL,
  {
    requestMiddleware,
    responseMiddleware,
    credentials: "include",
  },
  {
    enabled: true,
    includeFallbackQuery: IS_DEVELOPMENT_OR_STAGING,
    debug: IS_DEVELOPMENT_OR_STAGING,
  }
)
