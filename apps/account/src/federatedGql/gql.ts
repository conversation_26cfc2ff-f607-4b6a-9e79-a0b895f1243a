/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation acceptAdminTransfer($input: AcceptAdminTransferInput!) {\n    acceptAdminTransfer(input: $input) {\n      ... on AcceptAdminTransferRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.AcceptAdminTransferDocument,
    "\n  mutation createAgreement($appId: String) {\n    createAgreement(appId: $appId) {\n      code\n      message\n      reqId\n    }\n  }\n": typeof types.CreateAgreementDocument,
    "\n  query getEligibleApps($input: GetEligibleAppsFilter) {\n    getEligibleApps(filter: $input) {\n      ... on GetEligibleAppsResult {\n        __typename\n        applications {\n          url\n          name\n          id\n          tncRequired\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetEligibleAppsDocument,
    "\n  query getPersonaListForApplication($appId: String!) {\n    getPersonaListForApplication(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          companyName\n          klpdName\n          personaId\n          satkerName\n          satkerKode\n          isPenyedia\n          appRole\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetPersonaListForApplicationDocument,
    "\n  mutation personaRegisterRegisteredPenyedia(\n    $input: PersonaRegisterRegisteredPenyediaInput\n  ) {\n    personaRegisterRegisteredPenyedia(input: $input) {\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n      ... on PersonaRes {\n        id\n        __typename\n      }\n    }\n  }\n": typeof types.PersonaRegisterRegisteredPenyediaDocument,
    "\n  mutation personaRegisterNonPenyedia(\n    $input: PersonaRegisterNonPenyediaInput!\n  ) {\n    personaRegisterNonPenyedia(input: $input) {\n      ... on PersonaRes {\n        id\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": typeof types.PersonaRegisterNonPenyediaDocument,
    "\n  mutation personaRegisterNonRegisteredPenyedia(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyedia(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n": typeof types.PersonaRegisterNonRegisteredPenyediaDocument,
    "\n  mutation personaRegistrationNonPenyediaCompany(\n    $input: PersonaRegistrationNonPenyediaCompanyInput\n  ) {\n    personaRegistrationNonPenyediaCompany(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on PersonaRes {\n        id\n      }\n    }\n  }\n": typeof types.PersonaRegistrationNonPenyediaCompanyDocument,
    "\n  mutation personaRegisterNonRegisteredPenyediaV2(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyediaV2(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n": typeof types.PersonaRegisterNonRegisteredPenyediaV2Document,
    "\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckSatkerNpwpValidityDocument,
    "\n  query satker($filter: SatkerFilter, $pagination: AccountPagination!) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          alamat\n          namaSatker\n          kodeSatker\n          npwp\n          kodeSatkerAdj\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": typeof types.SatkerDocument,
    "\n  query personaValidateNpwp($npwp: String) {\n    personaValidateNPWP(npwp: $npwp) {\n      ... on PersonaValidateNPWPRes {\n        __typename\n        companyPersonaClaim {\n          bentukUsaha\n          companyAddresses {\n            fullAddress\n            isMainAddress\n            label\n            latitude\n            longitude\n            notes\n            phoneNumber\n            postalCode\n            villageAreaCode\n          }\n          companyName\n          isCabang\n          isPkp\n          npwp\n          nib\n          pkpNumber\n        }\n        isRegistered\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.PersonaValidateNpwpDocument,
    "\n  query personaClaimLegacyApp(\n    $app: ApplicationTypeEnum!\n    $username: String!\n    $password: String!\n    $lpseId: Int\n    $role: NonPenyediaRoleEnum!\n  ) {\n    personaClaimLegacyApp(\n      app: $app\n      password: $password\n      username: $username\n      lpseId: $lpseId\n      role: $role\n    ) {\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n      ... on LegacyAppPersonaClaimRes {\n        username\n        userData {\n          pegGolongan\n        }\n        __typename\n      }\n    }\n  }\n": typeof types.PersonaClaimLegacyAppDocument,
    "\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": typeof types.KlpdDocument,
    "\n  query eselon($pagination: AccountPagination!, $filter: EselonFilter) {\n    eselon(pagination: $pagination, filter: $filter) {\n      ... on EselonResult {\n        __typename\n        items {\n          namaEselon\n          kodeEselon\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": typeof types.EselonDocument,
    "\n  query getApplicationByFilter($filter: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $filter) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          lpseId\n          name\n          id\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.GetApplicationByFilterDocument,
    "\n  query getNipTiketCount {\n    getFinishedTicketCount(ticketCategory: NIP_TICKET) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetNipTiketCountDocument,
    "\n  query esignByFilter($provider: ContractProviderType!) {\n    esignByFilter(provider: $provider) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on ContractUsersResponse {\n        __typename\n        users {\n          userId\n          updatedAt\n          status\n          signToken\n          rejectReason\n          providerUserPhone\n          providerUserNik\n          providerUserName\n          providerUserId\n          providerUserEmail\n          provider\n          id\n        }\n      }\n    }\n  }\n": typeof types.EsignByFilterDocument,
    "\n  query statusEsignAccount($provider: ContractProviderType) {\n    statusEsignAccount(provider: $provider) {\n      ... on ContractUserResponse {\n        __typename\n        id\n        rejectReason\n        userId\n        status\n        signToken\n        providerUserPhone\n        providerUserNik\n        providerUserName\n        providerUserId\n        providerUserEmail\n        provider\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.StatusEsignAccountDocument,
    "\n  query checkNIBNonPenyedia($input: CheckNIBNonPenyediaInput!) {\n    checkNIBNonPenyedia(input: $input) {\n      ... on CheckNIBNonPenyediaType {\n        nib\n        name\n        isRegistered\n        npwp\n        formKey\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckNibNonPenyediaDocument,
    "\n  query checkNIB($nib: String!) {\n    checkNIB(nib: $nib) {\n      ... on CheckNIBType {\n        nib\n        isUmk\n        formKey\n        bentukUsaha\n        isRegistered\n        skalaUsaha\n        beneficialOwnership\n        namaPerseroan\n        npwpPerseroan\n        kswp\n        kswpValid\n        kswpDate\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnershipValid\n        npwpPerseroanValid\n        kbli {\n          id\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n          kbli\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.CheckNibDocument,
    "\n  query checkNIBV2($input: CheckNIBV2Input!) {\n    checkNIBV2(input: $input) {\n      ... on CheckNIBV2Type {\n        nib\n        isUmk\n        isPKP\n        djpCheck\n        djpNPWPCheck\n        djpNPWPValid\n        ahuCheck\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnership\n        beneficialOwnershipDate\n        beneficialOwnershipValid\n        bentukUsaha\n        formKey\n        isRegistered\n        kswp\n        kswpDate\n        kswpValid\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        sikapCheck\n        skalaUsaha\n        kbli {\n          id\n          kbli\n          name\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckNibv2Document,
    "\n  query checkNIBV2CreateStaff($input: CheckNIBV2CreateInput!, $nib: String!) {\n    checkNIBV2Create(input: $input, nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        kswpValid\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckNibv2CreateStaffDocument,
    "\n  query checkNIBV2Create($nib: String!) {\n    checkNIBV2Create(nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckNibv2CreateDocument,
    "\n  query checkNPWP($input: CheckNPWPInput!) {\n    checkNPWP(input: $input) {\n      ... on CheckNPWPType {\n        beneficialOwnership\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckNpwpDocument,
    "\n  query checkCertPPSDM($certNumber: String!) {\n    checkCertPPSDM(certNumber: $certNumber) {\n      ... on CheckCertPPSDMResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckCertPpsdmDocument,
    "\n  query getAddressReverseGeocode($lat: Float!, $lng: Float!) {\n    reverseGeocode(query: { latitude: $lat, longitude: $lng }) {\n      ... on ReverseGeocode {\n        __typename\n        villageAreaCode\n        villagePostalCode\n        villageName\n        cityName\n        districtName\n        provinceName\n        cityId\n        provinceId\n        districtId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetAddressReverseGeocodeDocument,
    "\n  query getKBLIList($codes: [String], $perPage: Int, $page: Int) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n": typeof types.GetKbliListDocument,
    "\n  query GetSearchRegion(\n    $level: RegionLevel\n    $search: String\n    $parentIds: [String!]\n    $pagination: RegionPagination!\n  ) {\n    getSearchRegionV2(\n      query: {\n        filter: { level: $level, search: $search, parentIds: $parentIds }\n        pagination: $pagination\n      }\n    ) {\n      ... on RegionSearchList {\n        items {\n          areaCode\n          id\n          level\n          name\n          parentId\n          postalCode\n        }\n        currentPage\n        lastPage\n        perPage\n        total\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetSearchRegionDocument,
    "\n  query getCuratorTypes($klpdCodes: [String!]!) {\n    curatorRoles(input: { klpdCodes: $klpdCodes }) {\n      ... on CuratorRoles {\n        __typename\n        items {\n          klpdCode\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetCuratorTypesDocument,
    "\n  mutation setCompanyPICTtd($input: SetCompanyPICTtdInput!) {\n    setCompanyPICTtd(input: $input) {\n      ... on CompanyType {\n        id\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n": typeof types.SetCompanyPicTtdDocument,
    "\n  query getProfileStatus {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        profileStatus\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetProfileStatusDocument,
    "\n  query getEnterpriseInstitutionId($filter: ListPersonaFilterInput) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetEnterpriseInstitutionIdDocument,
    "\n  query listPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.ListPersonaNonPenyediaDocument,
    "\n  query statusEnterprise($input: StatusEnterpriseInput!) {\n    statusEnterprise(input: $input) {\n      ... on StatusEnterprise {\n        __typename\n        rejectReason\n        bidangIndustri\n        companyName\n        enterpriseId\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.StatusEnterpriseDocument,
    "\n  query getEnterpriseMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          userId\n          status\n        }\n        users {\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetEnterpriseMemberListDocument,
    "\n  query getEnterprisePICTtd($filter: CompanyFilterInput) {\n    getCompanyByFilter(filter: $filter) {\n      ... on CompanyTypeList {\n        __typename\n        companies {\n          picTtd\n          name\n          institusiId\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetEnterprisePicTtdDocument,
    "\n  query signImage($userRole: UserRoleType) {\n    signImage(userRole: $userRole) {\n      ... on GetSignImageResponse {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.SignImageDocument,
    "\n  query getCompanyName($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n      }\n    }\n  }\n": typeof types.GetCompanyNameDocument,
    "\n  query enterpriseBalance($input: EnterpriseBalanceInput!) {\n    enterpriseBalance(input: $input) {\n      ... on EnterpriseBalance {\n        __typename\n        meterai\n        sign\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.EnterpriseBalanceDocument,
    "\n  query enterpriseTopUpUrl($input: EnterpriseTopUpUrlInput!) {\n    enterpriseTopUpUrl(input: $input) {\n      ... on EnterpriseTopUpUrlResp {\n        __typename\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.EnterpriseTopUpUrlDocument,
    "\n  mutation createQuestionnaire($input: [QuestionnaireInput!]) {\n    createQuestionnaire(input: $input) {\n      ... on QuestionnaireResult {\n        __typename\n        ids\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.CreateQuestionnaireDocument,
    "\n  query getApplicationLPSE($input: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $input) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          id\n          lpseId\n          name\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.GetApplicationLpseDocument,
    "\n  mutation updateNIP($nip: String!) {\n    updateNIP(nip: $nip) {\n      ... on UpdateNIPRes {\n        __typename\n        nip\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n": typeof types.UpdateNipDocument,
    "\n  query getAccountDetail {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        login\n        phone\n        email\n        type\n        isSeller\n        username\n        status\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetAccountDetailDocument,
    "\n  mutation updateCompanyBankStatus($input: UpdateCompanyBankStatusInput!) {\n    updateCompanyBankStatus(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UpdateCompanyBankStatusDocument,
    "\n  mutation addCompanyBank($input: AddCompanyBankInput!) {\n    addCompanyBank(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.AddCompanyBankDocument,
    "\n  mutation setMainCompanyBank($id: String!, $institusiId: String!) {\n    setMainCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.SetMainCompanyBankDocument,
    "\n  mutation deleteCompanyBank($id: String!, $institusiId: String!) {\n    deleteCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.DeleteCompanyBankDocument,
    "\n  query companyBankAccount($institusiId: String!) {\n    companyBankAccount(institusiId: $institusiId) {\n      ... on BankAccountList {\n        __typename\n        banks {\n          accountName\n          accountNo\n          bankCode\n          bankName\n          branchName\n          id\n          isMain\n          isVerify\n          status\n          createdAt\n          createdBy\n          updatedAt\n          updatedBy\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CompanyBankAccountDocument,
    "\n  mutation syncKBLI($input: SyncKBLIInput!) {\n    syncKBLI(input: $input) {\n      ... on SyncKBLIRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.SyncKbliDocument,
    "\n  mutation syncCompany($input: SyncCompanyInput!) {\n    syncCompany(input: $input) {\n      ... on SyncCompanyRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.SyncCompanyDocument,
    "\n  mutation updatePenyediaRole($input: UpdatePenyediaInput!) {\n    updatePenyedia(input: $input) {\n      ... on UpdatePenyediaResult {\n        __typename\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.UpdatePenyediaRoleDocument,
    "\n  query getPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        institusi {\n          id\n          institutionType\n        }\n        application {\n          name\n        }\n        klpd {\n          jenisKlpd\n          namaKlpd\n        }\n        company {\n          bidangIndustri\n          bentukUsaha\n          name\n          nib\n          npwp\n          npwpToken\n          suratPKPToken\n          updatedAt\n        }\n        persona {\n          institusiId\n          appRole\n          appUserData {\n            pegGolongan\n            lpseId\n          }\n          appUserName\n          expiredAt\n          id\n          isPenyedia\n          noPPSDM\n          status\n          unit\n          createdAt\n          updatedAt\n          rejectReason {\n            items\n            reason\n          }\n          noPPSDMToken\n          noPPSDM\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n        }\n        satker {\n          alamat\n          kodeSatker\n          namaSatker\n          npwp\n          npwpValid\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetPersonaNonPenyediaByIdDocument,
    "\n  query getPersonaPenyediaByID($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          aktaNotarisNo\n          beneficialOwnership\n          bentukUsaha\n          bidangIndustri\n          companyAddresses {\n            fullAddress\n          }\n          updatedAt\n          id\n          institusiId\n          isCabang\n          isPkp\n          isUkm\n          isUmkk\n          isNpwp16Valid\n          isNpwpValid\n          jenisPerusahaan\n          kswp\n          name\n          nib\n          noPkp\n          npwp\n          npwp16\n          oss {\n            project {\n              kbli {\n                kbli\n              }\n            }\n          }\n          picTtd\n          skalaUsaha\n          slug\n          status\n          telepon\n          umkType\n          username\n          website\n        }\n        penyediaFile {\n          nibUrl\n          npwpURL\n          suratPKPUrl\n          kswpURL\n        }\n        persona {\n          appRole\n          appUserName\n          id\n          institusiId\n          penyediaJabatan\n          status\n          rejectReason {\n            items\n            reason\n          }\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n          suratKuasaURL\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetPersonaPenyediaByIdDocument,
    "\n  query getRoleCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          userId\n          penyediaJabatan\n        }\n        users {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetRoleCompanyMemberListDocument,
    "\n  query getKBLIListV2(\n    $codes: [String]\n    $perPage: Int\n    $page: Int\n    $search: String\n  ) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page, search: $search) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n": typeof types.GetKbliListV2Document,
    "\n  mutation penyediaAdminTransfer($input: PenyediaAdminTransferInput!) {\n    penyediaAdminTransfer(input: $input) {\n      ... on PenyediaAdminTransferRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.PenyediaAdminTransferDocument,
    "\n  mutation acceptCompanyMembership($input: AcceptCompanyMembershipInput!) {\n    acceptCompanyMembership(input: $input) {\n      ... on AcceptCompanyMembershipRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.AcceptCompanyMembershipDocument,
    "\n  mutation removeCompanyMembership($input: RemoveCompanyMembershipInput!) {\n    removeCompanyMembership(input: $input) {\n      ... on RemoveCompanyMembershipRes {\n        __typename\n        id\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.RemoveCompanyMembershipDocument,
    "\n  query getApplicationName {\n    getApplicationByFilter(filter: { isPilot: true }) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetApplicationNameDocument,
    "\n  query getAllPersonaPenyedia {\n    listPersonaPenyedia(filter: { statuses: PERSONA_VERIFIED }) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiMemberId\n          companyId\n          companyName\n          id\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetAllPersonaPenyediaDocument,
    "\n  query getCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          suratKuasaToken\n          appId\n          suratKeteranganKerjaToken\n          userId\n          id\n          penyediaJabatan\n        }\n        users {\n          email\n          name\n          phone\n          status\n          profileStatus\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetCompanyMemberListDocument,
    "\n  query companyAdminTransferList($institutionId: String) {\n    companyAdminTransferList(institutionId: $institutionId) {\n      ... on RoleChangeList {\n        __typename\n        roleChanges {\n          id\n          institutionId\n          status\n          userIdTarget\n          userIdSource\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CompanyAdminTransferListDocument,
    "\n  mutation updatePersonaShippingAddress(\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updatePersonaShippingAddress(id: $id, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UpdatePersonaShippingAddressDocument,
    "\n  mutation updateCompanyShippingAddress(\n    $companyId: String!\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updateCompanyShippingAddress(\n      companyId: $companyId\n      id: $id\n      input: $input\n    ) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        regionDetail {\n          cityName\n          districtName\n          provinceName\n          villageName\n        }\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UpdateCompanyShippingAddressDocument,
    "\n  mutation deletePersonaShippingAddress($id: String!) {\n    deletePersonaShippingAddress(id: $id) {\n      ... on DeletePersonaShippingAddressResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.DeletePersonaShippingAddressDocument,
    "\n  mutation addPersonaShippingAddress(\n    $personaId: String!\n    $input: AddressesTypeInput!\n  ) {\n    addPersonaShippingAddress(personaId: $personaId, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.AddPersonaShippingAddressDocument,
    "\n  query getPersonaNonPenyediaShippingAddress($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        personaShippingAddress {\n          __typename\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetPersonaNonPenyediaShippingAddressDocument,
    "\n  query getPersonaPenyediaShippingAddress($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          id\n        }\n        persona {\n          appRole\n        }\n        personaShippingAddress {\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetPersonaPenyediaShippingAddressDocument,
    "\n  query getTraceParentRegion($areaCodes: [String!]) {\n    getTraceParentRegion(query: { areaCodes: $areaCodes }) {\n      items {\n        provinceAreaCode\n        provinceId\n        provinceName\n        cityAreaCode\n        cityId\n        cityName\n        districtAreaCode\n        districtId\n        districtName\n        villageAreaCode\n        villageId\n        villageName\n        villagePostalCode\n      }\n    }\n  }\n": typeof types.GetTraceParentRegionDocument,
    "\n  query getCompanyUserData($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        users {\n          id\n          email\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetCompanyUserDataDocument,
    "\n  query getFinishedTicketCount(\n    $ticketCategory: TicketCategory!\n    $ticketId: String!\n  ) {\n    getFinishedTicketCount(\n      ticketCategory: $ticketCategory\n      ticketId: $ticketId\n    ) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetFinishedTicketCountDocument,
    "\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.ReviewAccessPersonaDocument,
    "\n  query getStatusPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        persona {\n          id\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetStatusPersonaNonPenyediaByIdDocument,
    "\n  query getAccountData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        type\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetAccountDataDocument,
    "\n  mutation requestOTP($tiketId: String!, $category: OTPCategory!) {\n    sendOTP(category: $category, tiketId: $tiketId) {\n      ... on SendOTPResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n": typeof types.RequestOtpDocument,
    "\n  mutation updateEmail($tiketId: String!, $newEmail: String!) {\n    updateEmail(newEmail: $newEmail, tiketId: $tiketId) {\n      ... on UpdateEmailResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UpdateEmailDocument,
    "\n  mutation validateOTP(\n    $tiketId: String!\n    $otp: String!\n    $category: OTPCategory!\n  ) {\n    validateOTP(category: $category, otp: $otp, tiketId: $tiketId) {\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n      ... on ValidateOTPResult {\n        __typename\n        userId\n      }\n    }\n  }\n": typeof types.ValidateOtpDocument,
    "\n  query getUserData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.GetUserDataDocument,
    "\n  mutation reuploadEnterpriseFile($input: ReuploadEnterpriseFileInput!) {\n    reuploadEnterpriseFile(input: $input) {\n      ... on ReuploadEnterpriseFileRes {\n        __typename\n        institusiId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.ReuploadEnterpriseFileDocument,
    "\n  query getInstitutionById($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          npwp\n          name\n          oss {\n            companyType\n          }\n          isNpwpValid\n          npwp16\n          npwp\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetInstitutionByIdDocument,
    "\n  query downloadAccount($tokens: [String!]!) {\n    downloadAccount(tokens: $tokens) {\n      ... on DownloadAccount {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.DownloadAccountDocument,
    "\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.UploadAccountDocument,
    "\n  mutation uploadAccountPublicEnc($input: [UploadAccountPublicRequest]) {\n    uploadAccountPublic(input: $input) {\n      ... on UploadAccountPublicSuccess {\n        __typename\n        result {\n          identifier\n          publicKey\n          signedUrl\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.UploadAccountPublicEncDocument,
    "\n  mutation updateNextOnboardingStep {\n    updateNextOnboardingStep {\n      ... on GetOnboardingStatus {\n        __typename\n        nextStep\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.UpdateNextOnboardingStepDocument,
    "\n  mutation setContractAuthCodeAccount($input: SetAuthCodeRequest) {\n    setContractAuthCodeAccount(input: $input) {\n      id\n      userId\n      provider\n      providerUserId\n      providerUserName\n      providerUserEmail\n      providerUserPhone\n      providerUserNik\n      status\n      signToken\n      rejectReason\n      updatedAt\n    }\n  }\n": typeof types.SetContractAuthCodeAccountDocument,
    "\n  mutation updateVerificationProcess($input: UpdateVerificationProcessInput!) {\n    updateVerificationProcess(input: $input) {\n      code\n      message\n      reqId\n      __typename\n    }\n  }\n": typeof types.UpdateVerificationProcessDocument,
    "\n  query getStatusOnboarding {\n    getStatusOnboarding {\n      ... on StatusOnBoardingInfo {\n        __typename\n        currentStep\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": typeof types.GetStatusOnboardingDocument,
    "\n  query getInterviewSchedule(\n    $personaId: String\n    $type: VerificationType! = USER\n  ) {\n    getInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": typeof types.GetInterviewScheduleDocument,
    "\n  query generateInterviewSchedule(\n    $type: VerificationType! = USER\n    $personaId: String\n  ) {\n    generateInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.GenerateInterviewScheduleDocument,
    "\n  query privyRegLinkAccount {\n    privyRegLinkAccount {\n      ... on PrivyRegLink {\n        __typename\n        registrationUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.PrivyRegLinkAccountDocument,
    "\n  query getPersonaListForPersonaChooser($appId: String!) {\n    getPersonaListForPersonaChooser(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          appRole\n          companyName\n          isPenyedia\n          klpdName\n          personaId\n          satkerKode\n          satkerName\n          status\n          tokenClaim {\n            institutionId\n            institutionType\n            isTest\n            legacyAppsUserId\n            legacyAppsUserIdStr\n            legacyAppsUsername\n            personaId\n            personaOwner\n            role\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetPersonaListForPersonaChooserDocument,
    "\n  query getAccountUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        type\n        isSeller\n        username\n        status\n        profileStatus\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetAccountUserInfoDocument,
    "\n  query getUserType {\n    me {\n      ... on UserInfo {\n        __typename\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetUserTypeDocument,
    "\n  query getUserNIP {\n    me {\n      ... on UserInfo {\n        __typename\n        profile {\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetUserNipDocument,
    "\n  mutation uploadAccountEncProcess($input: [UploadAccountEncProcessRequest]) {\n    uploadAccountEncProcess(input: $input) {\n      ... on UploadAccountEncProcessResult {\n        __typename\n        result {\n          identifier\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UploadAccountEncProcessDocument,
    "\n  query getListPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          appId\n          id\n          companyId\n          companyName\n          institusiId\n          institusiMemberId\n          klpdName\n          role\n          satkerKode\n          satkerName\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetListPersonaNonPenyediaDocument,
    "\n  query getListPersonaPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          appId\n          companyId\n          companyName\n          id\n          institusiId\n          institusiMemberId\n          role\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetListPersonaPenyediaDocument,
    "\n  query uploadAccountEnc {\n    uploadAccountEnc {\n      ... on UploadAccountEncResult {\n        __typename\n        key\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UploadAccountEncDocument,
    "\n  query getSubscriberHash {\n    subscriberHash {\n      subscriberHash\n      subscriberId\n    }\n  }\n": typeof types.GetSubscriberHashDocument,
};
const documents: Documents = {
    "\n  mutation acceptAdminTransfer($input: AcceptAdminTransferInput!) {\n    acceptAdminTransfer(input: $input) {\n      ... on AcceptAdminTransferRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.AcceptAdminTransferDocument,
    "\n  mutation createAgreement($appId: String) {\n    createAgreement(appId: $appId) {\n      code\n      message\n      reqId\n    }\n  }\n": types.CreateAgreementDocument,
    "\n  query getEligibleApps($input: GetEligibleAppsFilter) {\n    getEligibleApps(filter: $input) {\n      ... on GetEligibleAppsResult {\n        __typename\n        applications {\n          url\n          name\n          id\n          tncRequired\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetEligibleAppsDocument,
    "\n  query getPersonaListForApplication($appId: String!) {\n    getPersonaListForApplication(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          companyName\n          klpdName\n          personaId\n          satkerName\n          satkerKode\n          isPenyedia\n          appRole\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetPersonaListForApplicationDocument,
    "\n  mutation personaRegisterRegisteredPenyedia(\n    $input: PersonaRegisterRegisteredPenyediaInput\n  ) {\n    personaRegisterRegisteredPenyedia(input: $input) {\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n      ... on PersonaRes {\n        id\n        __typename\n      }\n    }\n  }\n": types.PersonaRegisterRegisteredPenyediaDocument,
    "\n  mutation personaRegisterNonPenyedia(\n    $input: PersonaRegisterNonPenyediaInput!\n  ) {\n    personaRegisterNonPenyedia(input: $input) {\n      ... on PersonaRes {\n        id\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": types.PersonaRegisterNonPenyediaDocument,
    "\n  mutation personaRegisterNonRegisteredPenyedia(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyedia(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n": types.PersonaRegisterNonRegisteredPenyediaDocument,
    "\n  mutation personaRegistrationNonPenyediaCompany(\n    $input: PersonaRegistrationNonPenyediaCompanyInput\n  ) {\n    personaRegistrationNonPenyediaCompany(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on PersonaRes {\n        id\n      }\n    }\n  }\n": types.PersonaRegistrationNonPenyediaCompanyDocument,
    "\n  mutation personaRegisterNonRegisteredPenyediaV2(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyediaV2(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n": types.PersonaRegisterNonRegisteredPenyediaV2Document,
    "\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckSatkerNpwpValidityDocument,
    "\n  query satker($filter: SatkerFilter, $pagination: AccountPagination!) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          alamat\n          namaSatker\n          kodeSatker\n          npwp\n          kodeSatkerAdj\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": types.SatkerDocument,
    "\n  query personaValidateNpwp($npwp: String) {\n    personaValidateNPWP(npwp: $npwp) {\n      ... on PersonaValidateNPWPRes {\n        __typename\n        companyPersonaClaim {\n          bentukUsaha\n          companyAddresses {\n            fullAddress\n            isMainAddress\n            label\n            latitude\n            longitude\n            notes\n            phoneNumber\n            postalCode\n            villageAreaCode\n          }\n          companyName\n          isCabang\n          isPkp\n          npwp\n          nib\n          pkpNumber\n        }\n        isRegistered\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.PersonaValidateNpwpDocument,
    "\n  query personaClaimLegacyApp(\n    $app: ApplicationTypeEnum!\n    $username: String!\n    $password: String!\n    $lpseId: Int\n    $role: NonPenyediaRoleEnum!\n  ) {\n    personaClaimLegacyApp(\n      app: $app\n      password: $password\n      username: $username\n      lpseId: $lpseId\n      role: $role\n    ) {\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n      ... on LegacyAppPersonaClaimRes {\n        username\n        userData {\n          pegGolongan\n        }\n        __typename\n      }\n    }\n  }\n": types.PersonaClaimLegacyAppDocument,
    "\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": types.KlpdDocument,
    "\n  query eselon($pagination: AccountPagination!, $filter: EselonFilter) {\n    eselon(pagination: $pagination, filter: $filter) {\n      ... on EselonResult {\n        __typename\n        items {\n          namaEselon\n          kodeEselon\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": types.EselonDocument,
    "\n  query getApplicationByFilter($filter: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $filter) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          lpseId\n          name\n          id\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.GetApplicationByFilterDocument,
    "\n  query getNipTiketCount {\n    getFinishedTicketCount(ticketCategory: NIP_TICKET) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetNipTiketCountDocument,
    "\n  query esignByFilter($provider: ContractProviderType!) {\n    esignByFilter(provider: $provider) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on ContractUsersResponse {\n        __typename\n        users {\n          userId\n          updatedAt\n          status\n          signToken\n          rejectReason\n          providerUserPhone\n          providerUserNik\n          providerUserName\n          providerUserId\n          providerUserEmail\n          provider\n          id\n        }\n      }\n    }\n  }\n": types.EsignByFilterDocument,
    "\n  query statusEsignAccount($provider: ContractProviderType) {\n    statusEsignAccount(provider: $provider) {\n      ... on ContractUserResponse {\n        __typename\n        id\n        rejectReason\n        userId\n        status\n        signToken\n        providerUserPhone\n        providerUserNik\n        providerUserName\n        providerUserId\n        providerUserEmail\n        provider\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.StatusEsignAccountDocument,
    "\n  query checkNIBNonPenyedia($input: CheckNIBNonPenyediaInput!) {\n    checkNIBNonPenyedia(input: $input) {\n      ... on CheckNIBNonPenyediaType {\n        nib\n        name\n        isRegistered\n        npwp\n        formKey\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckNibNonPenyediaDocument,
    "\n  query checkNIB($nib: String!) {\n    checkNIB(nib: $nib) {\n      ... on CheckNIBType {\n        nib\n        isUmk\n        formKey\n        bentukUsaha\n        isRegistered\n        skalaUsaha\n        beneficialOwnership\n        namaPerseroan\n        npwpPerseroan\n        kswp\n        kswpValid\n        kswpDate\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnershipValid\n        npwpPerseroanValid\n        kbli {\n          id\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n          kbli\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n": types.CheckNibDocument,
    "\n  query checkNIBV2($input: CheckNIBV2Input!) {\n    checkNIBV2(input: $input) {\n      ... on CheckNIBV2Type {\n        nib\n        isUmk\n        isPKP\n        djpCheck\n        djpNPWPCheck\n        djpNPWPValid\n        ahuCheck\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnership\n        beneficialOwnershipDate\n        beneficialOwnershipValid\n        bentukUsaha\n        formKey\n        isRegistered\n        kswp\n        kswpDate\n        kswpValid\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        sikapCheck\n        skalaUsaha\n        kbli {\n          id\n          kbli\n          name\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckNibv2Document,
    "\n  query checkNIBV2CreateStaff($input: CheckNIBV2CreateInput!, $nib: String!) {\n    checkNIBV2Create(input: $input, nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        kswpValid\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckNibv2CreateStaffDocument,
    "\n  query checkNIBV2Create($nib: String!) {\n    checkNIBV2Create(nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckNibv2CreateDocument,
    "\n  query checkNPWP($input: CheckNPWPInput!) {\n    checkNPWP(input: $input) {\n      ... on CheckNPWPType {\n        beneficialOwnership\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckNpwpDocument,
    "\n  query checkCertPPSDM($certNumber: String!) {\n    checkCertPPSDM(certNumber: $certNumber) {\n      ... on CheckCertPPSDMResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckCertPpsdmDocument,
    "\n  query getAddressReverseGeocode($lat: Float!, $lng: Float!) {\n    reverseGeocode(query: { latitude: $lat, longitude: $lng }) {\n      ... on ReverseGeocode {\n        __typename\n        villageAreaCode\n        villagePostalCode\n        villageName\n        cityName\n        districtName\n        provinceName\n        cityId\n        provinceId\n        districtId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetAddressReverseGeocodeDocument,
    "\n  query getKBLIList($codes: [String], $perPage: Int, $page: Int) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n": types.GetKbliListDocument,
    "\n  query GetSearchRegion(\n    $level: RegionLevel\n    $search: String\n    $parentIds: [String!]\n    $pagination: RegionPagination!\n  ) {\n    getSearchRegionV2(\n      query: {\n        filter: { level: $level, search: $search, parentIds: $parentIds }\n        pagination: $pagination\n      }\n    ) {\n      ... on RegionSearchList {\n        items {\n          areaCode\n          id\n          level\n          name\n          parentId\n          postalCode\n        }\n        currentPage\n        lastPage\n        perPage\n        total\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetSearchRegionDocument,
    "\n  query getCuratorTypes($klpdCodes: [String!]!) {\n    curatorRoles(input: { klpdCodes: $klpdCodes }) {\n      ... on CuratorRoles {\n        __typename\n        items {\n          klpdCode\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetCuratorTypesDocument,
    "\n  mutation setCompanyPICTtd($input: SetCompanyPICTtdInput!) {\n    setCompanyPICTtd(input: $input) {\n      ... on CompanyType {\n        id\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n": types.SetCompanyPicTtdDocument,
    "\n  query getProfileStatus {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        profileStatus\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetProfileStatusDocument,
    "\n  query getEnterpriseInstitutionId($filter: ListPersonaFilterInput) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetEnterpriseInstitutionIdDocument,
    "\n  query listPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.ListPersonaNonPenyediaDocument,
    "\n  query statusEnterprise($input: StatusEnterpriseInput!) {\n    statusEnterprise(input: $input) {\n      ... on StatusEnterprise {\n        __typename\n        rejectReason\n        bidangIndustri\n        companyName\n        enterpriseId\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.StatusEnterpriseDocument,
    "\n  query getEnterpriseMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          userId\n          status\n        }\n        users {\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetEnterpriseMemberListDocument,
    "\n  query getEnterprisePICTtd($filter: CompanyFilterInput) {\n    getCompanyByFilter(filter: $filter) {\n      ... on CompanyTypeList {\n        __typename\n        companies {\n          picTtd\n          name\n          institusiId\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetEnterprisePicTtdDocument,
    "\n  query signImage($userRole: UserRoleType) {\n    signImage(userRole: $userRole) {\n      ... on GetSignImageResponse {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.SignImageDocument,
    "\n  query getCompanyName($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n      }\n    }\n  }\n": types.GetCompanyNameDocument,
    "\n  query enterpriseBalance($input: EnterpriseBalanceInput!) {\n    enterpriseBalance(input: $input) {\n      ... on EnterpriseBalance {\n        __typename\n        meterai\n        sign\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.EnterpriseBalanceDocument,
    "\n  query enterpriseTopUpUrl($input: EnterpriseTopUpUrlInput!) {\n    enterpriseTopUpUrl(input: $input) {\n      ... on EnterpriseTopUpUrlResp {\n        __typename\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.EnterpriseTopUpUrlDocument,
    "\n  mutation createQuestionnaire($input: [QuestionnaireInput!]) {\n    createQuestionnaire(input: $input) {\n      ... on QuestionnaireResult {\n        __typename\n        ids\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.CreateQuestionnaireDocument,
    "\n  query getApplicationLPSE($input: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $input) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          id\n          lpseId\n          name\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.GetApplicationLpseDocument,
    "\n  mutation updateNIP($nip: String!) {\n    updateNIP(nip: $nip) {\n      ... on UpdateNIPRes {\n        __typename\n        nip\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n": types.UpdateNipDocument,
    "\n  query getAccountDetail {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        login\n        phone\n        email\n        type\n        isSeller\n        username\n        status\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetAccountDetailDocument,
    "\n  mutation updateCompanyBankStatus($input: UpdateCompanyBankStatusInput!) {\n    updateCompanyBankStatus(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UpdateCompanyBankStatusDocument,
    "\n  mutation addCompanyBank($input: AddCompanyBankInput!) {\n    addCompanyBank(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.AddCompanyBankDocument,
    "\n  mutation setMainCompanyBank($id: String!, $institusiId: String!) {\n    setMainCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.SetMainCompanyBankDocument,
    "\n  mutation deleteCompanyBank($id: String!, $institusiId: String!) {\n    deleteCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.DeleteCompanyBankDocument,
    "\n  query companyBankAccount($institusiId: String!) {\n    companyBankAccount(institusiId: $institusiId) {\n      ... on BankAccountList {\n        __typename\n        banks {\n          accountName\n          accountNo\n          bankCode\n          bankName\n          branchName\n          id\n          isMain\n          isVerify\n          status\n          createdAt\n          createdBy\n          updatedAt\n          updatedBy\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CompanyBankAccountDocument,
    "\n  mutation syncKBLI($input: SyncKBLIInput!) {\n    syncKBLI(input: $input) {\n      ... on SyncKBLIRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.SyncKbliDocument,
    "\n  mutation syncCompany($input: SyncCompanyInput!) {\n    syncCompany(input: $input) {\n      ... on SyncCompanyRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.SyncCompanyDocument,
    "\n  mutation updatePenyediaRole($input: UpdatePenyediaInput!) {\n    updatePenyedia(input: $input) {\n      ... on UpdatePenyediaResult {\n        __typename\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.UpdatePenyediaRoleDocument,
    "\n  query getPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        institusi {\n          id\n          institutionType\n        }\n        application {\n          name\n        }\n        klpd {\n          jenisKlpd\n          namaKlpd\n        }\n        company {\n          bidangIndustri\n          bentukUsaha\n          name\n          nib\n          npwp\n          npwpToken\n          suratPKPToken\n          updatedAt\n        }\n        persona {\n          institusiId\n          appRole\n          appUserData {\n            pegGolongan\n            lpseId\n          }\n          appUserName\n          expiredAt\n          id\n          isPenyedia\n          noPPSDM\n          status\n          unit\n          createdAt\n          updatedAt\n          rejectReason {\n            items\n            reason\n          }\n          noPPSDMToken\n          noPPSDM\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n        }\n        satker {\n          alamat\n          kodeSatker\n          namaSatker\n          npwp\n          npwpValid\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetPersonaNonPenyediaByIdDocument,
    "\n  query getPersonaPenyediaByID($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          aktaNotarisNo\n          beneficialOwnership\n          bentukUsaha\n          bidangIndustri\n          companyAddresses {\n            fullAddress\n          }\n          updatedAt\n          id\n          institusiId\n          isCabang\n          isPkp\n          isUkm\n          isUmkk\n          isNpwp16Valid\n          isNpwpValid\n          jenisPerusahaan\n          kswp\n          name\n          nib\n          noPkp\n          npwp\n          npwp16\n          oss {\n            project {\n              kbli {\n                kbli\n              }\n            }\n          }\n          picTtd\n          skalaUsaha\n          slug\n          status\n          telepon\n          umkType\n          username\n          website\n        }\n        penyediaFile {\n          nibUrl\n          npwpURL\n          suratPKPUrl\n          kswpURL\n        }\n        persona {\n          appRole\n          appUserName\n          id\n          institusiId\n          penyediaJabatan\n          status\n          rejectReason {\n            items\n            reason\n          }\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n          suratKuasaURL\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetPersonaPenyediaByIdDocument,
    "\n  query getRoleCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          userId\n          penyediaJabatan\n        }\n        users {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetRoleCompanyMemberListDocument,
    "\n  query getKBLIListV2(\n    $codes: [String]\n    $perPage: Int\n    $page: Int\n    $search: String\n  ) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page, search: $search) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n": types.GetKbliListV2Document,
    "\n  mutation penyediaAdminTransfer($input: PenyediaAdminTransferInput!) {\n    penyediaAdminTransfer(input: $input) {\n      ... on PenyediaAdminTransferRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.PenyediaAdminTransferDocument,
    "\n  mutation acceptCompanyMembership($input: AcceptCompanyMembershipInput!) {\n    acceptCompanyMembership(input: $input) {\n      ... on AcceptCompanyMembershipRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.AcceptCompanyMembershipDocument,
    "\n  mutation removeCompanyMembership($input: RemoveCompanyMembershipInput!) {\n    removeCompanyMembership(input: $input) {\n      ... on RemoveCompanyMembershipRes {\n        __typename\n        id\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.RemoveCompanyMembershipDocument,
    "\n  query getApplicationName {\n    getApplicationByFilter(filter: { isPilot: true }) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetApplicationNameDocument,
    "\n  query getAllPersonaPenyedia {\n    listPersonaPenyedia(filter: { statuses: PERSONA_VERIFIED }) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiMemberId\n          companyId\n          companyName\n          id\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetAllPersonaPenyediaDocument,
    "\n  query getCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          suratKuasaToken\n          appId\n          suratKeteranganKerjaToken\n          userId\n          id\n          penyediaJabatan\n        }\n        users {\n          email\n          name\n          phone\n          status\n          profileStatus\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetCompanyMemberListDocument,
    "\n  query companyAdminTransferList($institutionId: String) {\n    companyAdminTransferList(institutionId: $institutionId) {\n      ... on RoleChangeList {\n        __typename\n        roleChanges {\n          id\n          institutionId\n          status\n          userIdTarget\n          userIdSource\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CompanyAdminTransferListDocument,
    "\n  mutation updatePersonaShippingAddress(\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updatePersonaShippingAddress(id: $id, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UpdatePersonaShippingAddressDocument,
    "\n  mutation updateCompanyShippingAddress(\n    $companyId: String!\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updateCompanyShippingAddress(\n      companyId: $companyId\n      id: $id\n      input: $input\n    ) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        regionDetail {\n          cityName\n          districtName\n          provinceName\n          villageName\n        }\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UpdateCompanyShippingAddressDocument,
    "\n  mutation deletePersonaShippingAddress($id: String!) {\n    deletePersonaShippingAddress(id: $id) {\n      ... on DeletePersonaShippingAddressResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.DeletePersonaShippingAddressDocument,
    "\n  mutation addPersonaShippingAddress(\n    $personaId: String!\n    $input: AddressesTypeInput!\n  ) {\n    addPersonaShippingAddress(personaId: $personaId, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.AddPersonaShippingAddressDocument,
    "\n  query getPersonaNonPenyediaShippingAddress($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        personaShippingAddress {\n          __typename\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetPersonaNonPenyediaShippingAddressDocument,
    "\n  query getPersonaPenyediaShippingAddress($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          id\n        }\n        persona {\n          appRole\n        }\n        personaShippingAddress {\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetPersonaPenyediaShippingAddressDocument,
    "\n  query getTraceParentRegion($areaCodes: [String!]) {\n    getTraceParentRegion(query: { areaCodes: $areaCodes }) {\n      items {\n        provinceAreaCode\n        provinceId\n        provinceName\n        cityAreaCode\n        cityId\n        cityName\n        districtAreaCode\n        districtId\n        districtName\n        villageAreaCode\n        villageId\n        villageName\n        villagePostalCode\n      }\n    }\n  }\n": types.GetTraceParentRegionDocument,
    "\n  query getCompanyUserData($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        users {\n          id\n          email\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetCompanyUserDataDocument,
    "\n  query getFinishedTicketCount(\n    $ticketCategory: TicketCategory!\n    $ticketId: String!\n  ) {\n    getFinishedTicketCount(\n      ticketCategory: $ticketCategory\n      ticketId: $ticketId\n    ) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetFinishedTicketCountDocument,
    "\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.ReviewAccessPersonaDocument,
    "\n  query getStatusPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        persona {\n          id\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetStatusPersonaNonPenyediaByIdDocument,
    "\n  query getAccountData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        type\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetAccountDataDocument,
    "\n  mutation requestOTP($tiketId: String!, $category: OTPCategory!) {\n    sendOTP(category: $category, tiketId: $tiketId) {\n      ... on SendOTPResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n": types.RequestOtpDocument,
    "\n  mutation updateEmail($tiketId: String!, $newEmail: String!) {\n    updateEmail(newEmail: $newEmail, tiketId: $tiketId) {\n      ... on UpdateEmailResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UpdateEmailDocument,
    "\n  mutation validateOTP(\n    $tiketId: String!\n    $otp: String!\n    $category: OTPCategory!\n  ) {\n    validateOTP(category: $category, otp: $otp, tiketId: $tiketId) {\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n      ... on ValidateOTPResult {\n        __typename\n        userId\n      }\n    }\n  }\n": types.ValidateOtpDocument,
    "\n  query getUserData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.GetUserDataDocument,
    "\n  mutation reuploadEnterpriseFile($input: ReuploadEnterpriseFileInput!) {\n    reuploadEnterpriseFile(input: $input) {\n      ... on ReuploadEnterpriseFileRes {\n        __typename\n        institusiId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.ReuploadEnterpriseFileDocument,
    "\n  query getInstitutionById($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          npwp\n          name\n          oss {\n            companyType\n          }\n          isNpwpValid\n          npwp16\n          npwp\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetInstitutionByIdDocument,
    "\n  query downloadAccount($tokens: [String!]!) {\n    downloadAccount(tokens: $tokens) {\n      ... on DownloadAccount {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.DownloadAccountDocument,
    "\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.UploadAccountDocument,
    "\n  mutation uploadAccountPublicEnc($input: [UploadAccountPublicRequest]) {\n    uploadAccountPublic(input: $input) {\n      ... on UploadAccountPublicSuccess {\n        __typename\n        result {\n          identifier\n          publicKey\n          signedUrl\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.UploadAccountPublicEncDocument,
    "\n  mutation updateNextOnboardingStep {\n    updateNextOnboardingStep {\n      ... on GetOnboardingStatus {\n        __typename\n        nextStep\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.UpdateNextOnboardingStepDocument,
    "\n  mutation setContractAuthCodeAccount($input: SetAuthCodeRequest) {\n    setContractAuthCodeAccount(input: $input) {\n      id\n      userId\n      provider\n      providerUserId\n      providerUserName\n      providerUserEmail\n      providerUserPhone\n      providerUserNik\n      status\n      signToken\n      rejectReason\n      updatedAt\n    }\n  }\n": types.SetContractAuthCodeAccountDocument,
    "\n  mutation updateVerificationProcess($input: UpdateVerificationProcessInput!) {\n    updateVerificationProcess(input: $input) {\n      code\n      message\n      reqId\n      __typename\n    }\n  }\n": types.UpdateVerificationProcessDocument,
    "\n  query getStatusOnboarding {\n    getStatusOnboarding {\n      ... on StatusOnBoardingInfo {\n        __typename\n        currentStep\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": types.GetStatusOnboardingDocument,
    "\n  query getInterviewSchedule(\n    $personaId: String\n    $type: VerificationType! = USER\n  ) {\n    getInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": types.GetInterviewScheduleDocument,
    "\n  query generateInterviewSchedule(\n    $type: VerificationType! = USER\n    $personaId: String\n  ) {\n    generateInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.GenerateInterviewScheduleDocument,
    "\n  query privyRegLinkAccount {\n    privyRegLinkAccount {\n      ... on PrivyRegLink {\n        __typename\n        registrationUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.PrivyRegLinkAccountDocument,
    "\n  query getPersonaListForPersonaChooser($appId: String!) {\n    getPersonaListForPersonaChooser(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          appRole\n          companyName\n          isPenyedia\n          klpdName\n          personaId\n          satkerKode\n          satkerName\n          status\n          tokenClaim {\n            institutionId\n            institutionType\n            isTest\n            legacyAppsUserId\n            legacyAppsUserIdStr\n            legacyAppsUsername\n            personaId\n            personaOwner\n            role\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetPersonaListForPersonaChooserDocument,
    "\n  query getAccountUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        type\n        isSeller\n        username\n        status\n        profileStatus\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetAccountUserInfoDocument,
    "\n  query getUserType {\n    me {\n      ... on UserInfo {\n        __typename\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetUserTypeDocument,
    "\n  query getUserNIP {\n    me {\n      ... on UserInfo {\n        __typename\n        profile {\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetUserNipDocument,
    "\n  mutation uploadAccountEncProcess($input: [UploadAccountEncProcessRequest]) {\n    uploadAccountEncProcess(input: $input) {\n      ... on UploadAccountEncProcessResult {\n        __typename\n        result {\n          identifier\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UploadAccountEncProcessDocument,
    "\n  query getListPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          appId\n          id\n          companyId\n          companyName\n          institusiId\n          institusiMemberId\n          klpdName\n          role\n          satkerKode\n          satkerName\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetListPersonaNonPenyediaDocument,
    "\n  query getListPersonaPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          appId\n          companyId\n          companyName\n          id\n          institusiId\n          institusiMemberId\n          role\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetListPersonaPenyediaDocument,
    "\n  query uploadAccountEnc {\n    uploadAccountEnc {\n      ... on UploadAccountEncResult {\n        __typename\n        key\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UploadAccountEncDocument,
    "\n  query getSubscriberHash {\n    subscriberHash {\n      subscriberHash\n      subscriberId\n    }\n  }\n": types.GetSubscriberHashDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation acceptAdminTransfer($input: AcceptAdminTransferInput!) {\n    acceptAdminTransfer(input: $input) {\n      ... on AcceptAdminTransferRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation acceptAdminTransfer($input: AcceptAdminTransferInput!) {\n    acceptAdminTransfer(input: $input) {\n      ... on AcceptAdminTransferRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation createAgreement($appId: String) {\n    createAgreement(appId: $appId) {\n      code\n      message\n      reqId\n    }\n  }\n"): (typeof documents)["\n  mutation createAgreement($appId: String) {\n    createAgreement(appId: $appId) {\n      code\n      message\n      reqId\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getEligibleApps($input: GetEligibleAppsFilter) {\n    getEligibleApps(filter: $input) {\n      ... on GetEligibleAppsResult {\n        __typename\n        applications {\n          url\n          name\n          id\n          tncRequired\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getEligibleApps($input: GetEligibleAppsFilter) {\n    getEligibleApps(filter: $input) {\n      ... on GetEligibleAppsResult {\n        __typename\n        applications {\n          url\n          name\n          id\n          tncRequired\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getPersonaListForApplication($appId: String!) {\n    getPersonaListForApplication(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          companyName\n          klpdName\n          personaId\n          satkerName\n          satkerKode\n          isPenyedia\n          appRole\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getPersonaListForApplication($appId: String!) {\n    getPersonaListForApplication(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          companyName\n          klpdName\n          personaId\n          satkerName\n          satkerKode\n          isPenyedia\n          appRole\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation personaRegisterRegisteredPenyedia(\n    $input: PersonaRegisterRegisteredPenyediaInput\n  ) {\n    personaRegisterRegisteredPenyedia(input: $input) {\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n      ... on PersonaRes {\n        id\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation personaRegisterRegisteredPenyedia(\n    $input: PersonaRegisterRegisteredPenyediaInput\n  ) {\n    personaRegisterRegisteredPenyedia(input: $input) {\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n      ... on PersonaRes {\n        id\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation personaRegisterNonPenyedia(\n    $input: PersonaRegisterNonPenyediaInput!\n  ) {\n    personaRegisterNonPenyedia(input: $input) {\n      ... on PersonaRes {\n        id\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation personaRegisterNonPenyedia(\n    $input: PersonaRegisterNonPenyediaInput!\n  ) {\n    personaRegisterNonPenyedia(input: $input) {\n      ... on PersonaRes {\n        id\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation personaRegisterNonRegisteredPenyedia(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyedia(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation personaRegisterNonRegisteredPenyedia(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyedia(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation personaRegistrationNonPenyediaCompany(\n    $input: PersonaRegistrationNonPenyediaCompanyInput\n  ) {\n    personaRegistrationNonPenyediaCompany(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on PersonaRes {\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation personaRegistrationNonPenyediaCompany(\n    $input: PersonaRegistrationNonPenyediaCompanyInput\n  ) {\n    personaRegistrationNonPenyediaCompany(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on PersonaRes {\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation personaRegisterNonRegisteredPenyediaV2(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyediaV2(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation personaRegisterNonRegisteredPenyediaV2(\n    $input: PersonaRegisterNonRegisteredPenyediaInput\n  ) {\n    personaRegisterNonRegisteredPenyediaV2(input: $input) {\n      ... on PersonaRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        code\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query satker($filter: SatkerFilter, $pagination: AccountPagination!) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          alamat\n          namaSatker\n          kodeSatker\n          npwp\n          kodeSatkerAdj\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  query satker($filter: SatkerFilter, $pagination: AccountPagination!) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          alamat\n          namaSatker\n          kodeSatker\n          npwp\n          kodeSatkerAdj\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query personaValidateNpwp($npwp: String) {\n    personaValidateNPWP(npwp: $npwp) {\n      ... on PersonaValidateNPWPRes {\n        __typename\n        companyPersonaClaim {\n          bentukUsaha\n          companyAddresses {\n            fullAddress\n            isMainAddress\n            label\n            latitude\n            longitude\n            notes\n            phoneNumber\n            postalCode\n            villageAreaCode\n          }\n          companyName\n          isCabang\n          isPkp\n          npwp\n          nib\n          pkpNumber\n        }\n        isRegistered\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query personaValidateNpwp($npwp: String) {\n    personaValidateNPWP(npwp: $npwp) {\n      ... on PersonaValidateNPWPRes {\n        __typename\n        companyPersonaClaim {\n          bentukUsaha\n          companyAddresses {\n            fullAddress\n            isMainAddress\n            label\n            latitude\n            longitude\n            notes\n            phoneNumber\n            postalCode\n            villageAreaCode\n          }\n          companyName\n          isCabang\n          isPkp\n          npwp\n          nib\n          pkpNumber\n        }\n        isRegistered\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query personaClaimLegacyApp(\n    $app: ApplicationTypeEnum!\n    $username: String!\n    $password: String!\n    $lpseId: Int\n    $role: NonPenyediaRoleEnum!\n  ) {\n    personaClaimLegacyApp(\n      app: $app\n      password: $password\n      username: $username\n      lpseId: $lpseId\n      role: $role\n    ) {\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n      ... on LegacyAppPersonaClaimRes {\n        username\n        userData {\n          pegGolongan\n        }\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  query personaClaimLegacyApp(\n    $app: ApplicationTypeEnum!\n    $username: String!\n    $password: String!\n    $lpseId: Int\n    $role: NonPenyediaRoleEnum!\n  ) {\n    personaClaimLegacyApp(\n      app: $app\n      password: $password\n      username: $username\n      lpseId: $lpseId\n      role: $role\n    ) {\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n      ... on LegacyAppPersonaClaimRes {\n        username\n        userData {\n          pegGolongan\n        }\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query eselon($pagination: AccountPagination!, $filter: EselonFilter) {\n    eselon(pagination: $pagination, filter: $filter) {\n      ... on EselonResult {\n        __typename\n        items {\n          namaEselon\n          kodeEselon\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  query eselon($pagination: AccountPagination!, $filter: EselonFilter) {\n    eselon(pagination: $pagination, filter: $filter) {\n      ... on EselonResult {\n        __typename\n        items {\n          namaEselon\n          kodeEselon\n        }\n        paginationInfo {\n          currentPage\n          total\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getApplicationByFilter($filter: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $filter) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          lpseId\n          name\n          id\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query getApplicationByFilter($filter: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $filter) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          lpseId\n          name\n          id\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getNipTiketCount {\n    getFinishedTicketCount(ticketCategory: NIP_TICKET) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getNipTiketCount {\n    getFinishedTicketCount(ticketCategory: NIP_TICKET) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query esignByFilter($provider: ContractProviderType!) {\n    esignByFilter(provider: $provider) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on ContractUsersResponse {\n        __typename\n        users {\n          userId\n          updatedAt\n          status\n          signToken\n          rejectReason\n          providerUserPhone\n          providerUserNik\n          providerUserName\n          providerUserId\n          providerUserEmail\n          provider\n          id\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query esignByFilter($provider: ContractProviderType!) {\n    esignByFilter(provider: $provider) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on ContractUsersResponse {\n        __typename\n        users {\n          userId\n          updatedAt\n          status\n          signToken\n          rejectReason\n          providerUserPhone\n          providerUserNik\n          providerUserName\n          providerUserId\n          providerUserEmail\n          provider\n          id\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query statusEsignAccount($provider: ContractProviderType) {\n    statusEsignAccount(provider: $provider) {\n      ... on ContractUserResponse {\n        __typename\n        id\n        rejectReason\n        userId\n        status\n        signToken\n        providerUserPhone\n        providerUserNik\n        providerUserName\n        providerUserId\n        providerUserEmail\n        provider\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query statusEsignAccount($provider: ContractProviderType) {\n    statusEsignAccount(provider: $provider) {\n      ... on ContractUserResponse {\n        __typename\n        id\n        rejectReason\n        userId\n        status\n        signToken\n        providerUserPhone\n        providerUserNik\n        providerUserName\n        providerUserId\n        providerUserEmail\n        provider\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query checkNIBNonPenyedia($input: CheckNIBNonPenyediaInput!) {\n    checkNIBNonPenyedia(input: $input) {\n      ... on CheckNIBNonPenyediaType {\n        nib\n        name\n        isRegistered\n        npwp\n        formKey\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query checkNIBNonPenyedia($input: CheckNIBNonPenyediaInput!) {\n    checkNIBNonPenyedia(input: $input) {\n      ... on CheckNIBNonPenyediaType {\n        nib\n        name\n        isRegistered\n        npwp\n        formKey\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query checkNIB($nib: String!) {\n    checkNIB(nib: $nib) {\n      ... on CheckNIBType {\n        nib\n        isUmk\n        formKey\n        bentukUsaha\n        isRegistered\n        skalaUsaha\n        beneficialOwnership\n        namaPerseroan\n        npwpPerseroan\n        kswp\n        kswpValid\n        kswpDate\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnershipValid\n        npwpPerseroanValid\n        kbli {\n          id\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n          kbli\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query checkNIB($nib: String!) {\n    checkNIB(nib: $nib) {\n      ... on CheckNIBType {\n        nib\n        isUmk\n        formKey\n        bentukUsaha\n        isRegistered\n        skalaUsaha\n        beneficialOwnership\n        namaPerseroan\n        npwpPerseroan\n        kswp\n        kswpValid\n        kswpDate\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnershipValid\n        npwpPerseroanValid\n        kbli {\n          id\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n          kbli\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query checkNIBV2($input: CheckNIBV2Input!) {\n    checkNIBV2(input: $input) {\n      ... on CheckNIBV2Type {\n        nib\n        isUmk\n        isPKP\n        djpCheck\n        djpNPWPCheck\n        djpNPWPValid\n        ahuCheck\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnership\n        beneficialOwnershipDate\n        beneficialOwnershipValid\n        bentukUsaha\n        formKey\n        isRegistered\n        kswp\n        kswpDate\n        kswpValid\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        sikapCheck\n        skalaUsaha\n        kbli {\n          id\n          kbli\n          name\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query checkNIBV2($input: CheckNIBV2Input!) {\n    checkNIBV2(input: $input) {\n      ... on CheckNIBV2Type {\n        nib\n        isUmk\n        isPKP\n        djpCheck\n        djpNPWPCheck\n        djpNPWPValid\n        ahuCheck\n        aktaNotarisNo\n        aktaNotarisNoValid\n        beneficialOwnership\n        beneficialOwnershipDate\n        beneficialOwnershipValid\n        bentukUsaha\n        formKey\n        isRegistered\n        kswp\n        kswpDate\n        kswpValid\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        sikapCheck\n        skalaUsaha\n        kbli {\n          id\n          kbli\n          name\n          izin {\n            number\n            code\n            expired\n            id\n            izinId\n            type\n            typeOss\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query checkNIBV2CreateStaff($input: CheckNIBV2CreateInput!, $nib: String!) {\n    checkNIBV2Create(input: $input, nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        kswpValid\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query checkNIBV2CreateStaff($input: CheckNIBV2CreateInput!, $nib: String!) {\n    checkNIBV2Create(input: $input, nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n        npwpPerseroan\n        npwpPerseroanValid\n        npwp16Digit\n        npwp16DigitValid\n        kswpValid\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query checkNIBV2Create($nib: String!) {\n    checkNIBV2Create(nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query checkNIBV2Create($nib: String!) {\n    checkNIBV2Create(nib: $nib) {\n      ... on CheckNIBV2Type {\n        nib\n        formKey\n        ahuCheck\n        sikapCheck\n        isRegistered\n        bentukUsaha\n        namaPerseroan\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query checkNPWP($input: CheckNPWPInput!) {\n    checkNPWP(input: $input) {\n      ... on CheckNPWPType {\n        beneficialOwnership\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query checkNPWP($input: CheckNPWPInput!) {\n    checkNPWP(input: $input) {\n      ... on CheckNPWPType {\n        beneficialOwnership\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query checkCertPPSDM($certNumber: String!) {\n    checkCertPPSDM(certNumber: $certNumber) {\n      ... on CheckCertPPSDMResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query checkCertPPSDM($certNumber: String!) {\n    checkCertPPSDM(certNumber: $certNumber) {\n      ... on CheckCertPPSDMResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getAddressReverseGeocode($lat: Float!, $lng: Float!) {\n    reverseGeocode(query: { latitude: $lat, longitude: $lng }) {\n      ... on ReverseGeocode {\n        __typename\n        villageAreaCode\n        villagePostalCode\n        villageName\n        cityName\n        districtName\n        provinceName\n        cityId\n        provinceId\n        districtId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getAddressReverseGeocode($lat: Float!, $lng: Float!) {\n    reverseGeocode(query: { latitude: $lat, longitude: $lng }) {\n      ... on ReverseGeocode {\n        __typename\n        villageAreaCode\n        villagePostalCode\n        villageName\n        cityName\n        districtName\n        provinceName\n        cityId\n        provinceId\n        districtId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getKBLIList($codes: [String], $perPage: Int, $page: Int) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n"): (typeof documents)["\n  query getKBLIList($codes: [String], $perPage: Int, $page: Int) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSearchRegion(\n    $level: RegionLevel\n    $search: String\n    $parentIds: [String!]\n    $pagination: RegionPagination!\n  ) {\n    getSearchRegionV2(\n      query: {\n        filter: { level: $level, search: $search, parentIds: $parentIds }\n        pagination: $pagination\n      }\n    ) {\n      ... on RegionSearchList {\n        items {\n          areaCode\n          id\n          level\n          name\n          parentId\n          postalCode\n        }\n        currentPage\n        lastPage\n        perPage\n        total\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSearchRegion(\n    $level: RegionLevel\n    $search: String\n    $parentIds: [String!]\n    $pagination: RegionPagination!\n  ) {\n    getSearchRegionV2(\n      query: {\n        filter: { level: $level, search: $search, parentIds: $parentIds }\n        pagination: $pagination\n      }\n    ) {\n      ... on RegionSearchList {\n        items {\n          areaCode\n          id\n          level\n          name\n          parentId\n          postalCode\n        }\n        currentPage\n        lastPage\n        perPage\n        total\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getCuratorTypes($klpdCodes: [String!]!) {\n    curatorRoles(input: { klpdCodes: $klpdCodes }) {\n      ... on CuratorRoles {\n        __typename\n        items {\n          klpdCode\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getCuratorTypes($klpdCodes: [String!]!) {\n    curatorRoles(input: { klpdCodes: $klpdCodes }) {\n      ... on CuratorRoles {\n        __typename\n        items {\n          klpdCode\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation setCompanyPICTtd($input: SetCompanyPICTtdInput!) {\n    setCompanyPICTtd(input: $input) {\n      ... on CompanyType {\n        id\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation setCompanyPICTtd($input: SetCompanyPICTtdInput!) {\n    setCompanyPICTtd(input: $input) {\n      ... on CompanyType {\n        id\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getProfileStatus {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        profileStatus\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getProfileStatus {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        profileStatus\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getEnterpriseInstitutionId($filter: ListPersonaFilterInput) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getEnterpriseInstitutionId($filter: ListPersonaFilterInput) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query listPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query listPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query statusEnterprise($input: StatusEnterpriseInput!) {\n    statusEnterprise(input: $input) {\n      ... on StatusEnterprise {\n        __typename\n        rejectReason\n        bidangIndustri\n        companyName\n        enterpriseId\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query statusEnterprise($input: StatusEnterpriseInput!) {\n    statusEnterprise(input: $input) {\n      ... on StatusEnterprise {\n        __typename\n        rejectReason\n        bidangIndustri\n        companyName\n        enterpriseId\n        status\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getEnterpriseMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          userId\n          status\n        }\n        users {\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getEnterpriseMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          userId\n          status\n        }\n        users {\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getEnterprisePICTtd($filter: CompanyFilterInput) {\n    getCompanyByFilter(filter: $filter) {\n      ... on CompanyTypeList {\n        __typename\n        companies {\n          picTtd\n          name\n          institusiId\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getEnterprisePICTtd($filter: CompanyFilterInput) {\n    getCompanyByFilter(filter: $filter) {\n      ... on CompanyTypeList {\n        __typename\n        companies {\n          picTtd\n          name\n          institusiId\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query signImage($userRole: UserRoleType) {\n    signImage(userRole: $userRole) {\n      ... on GetSignImageResponse {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query signImage($userRole: UserRoleType) {\n    signImage(userRole: $userRole) {\n      ... on GetSignImageResponse {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getCompanyName($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getCompanyName($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query enterpriseBalance($input: EnterpriseBalanceInput!) {\n    enterpriseBalance(input: $input) {\n      ... on EnterpriseBalance {\n        __typename\n        meterai\n        sign\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query enterpriseBalance($input: EnterpriseBalanceInput!) {\n    enterpriseBalance(input: $input) {\n      ... on EnterpriseBalance {\n        __typename\n        meterai\n        sign\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query enterpriseTopUpUrl($input: EnterpriseTopUpUrlInput!) {\n    enterpriseTopUpUrl(input: $input) {\n      ... on EnterpriseTopUpUrlResp {\n        __typename\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query enterpriseTopUpUrl($input: EnterpriseTopUpUrlInput!) {\n    enterpriseTopUpUrl(input: $input) {\n      ... on EnterpriseTopUpUrlResp {\n        __typename\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation createQuestionnaire($input: [QuestionnaireInput!]) {\n    createQuestionnaire(input: $input) {\n      ... on QuestionnaireResult {\n        __typename\n        ids\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation createQuestionnaire($input: [QuestionnaireInput!]) {\n    createQuestionnaire(input: $input) {\n      ... on QuestionnaireResult {\n        __typename\n        ids\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getApplicationLPSE($input: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $input) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          id\n          lpseId\n          name\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query getApplicationLPSE($input: ApplicationFilterInput) {\n    getApplicationByFilter(filter: $input) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          id\n          lpseId\n          name\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateNIP($nip: String!) {\n    updateNIP(nip: $nip) {\n      ... on UpdateNIPRes {\n        __typename\n        nip\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateNIP($nip: String!) {\n    updateNIP(nip: $nip) {\n      ... on UpdateNIPRes {\n        __typename\n        nip\n      }\n      ... on GenericError {\n        __typename\n        message\n        code\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getAccountDetail {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        login\n        phone\n        email\n        type\n        isSeller\n        username\n        status\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getAccountDetail {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        login\n        phone\n        email\n        type\n        isSeller\n        username\n        status\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateCompanyBankStatus($input: UpdateCompanyBankStatusInput!) {\n    updateCompanyBankStatus(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateCompanyBankStatus($input: UpdateCompanyBankStatusInput!) {\n    updateCompanyBankStatus(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation addCompanyBank($input: AddCompanyBankInput!) {\n    addCompanyBank(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation addCompanyBank($input: AddCompanyBankInput!) {\n    addCompanyBank(input: $input) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation setMainCompanyBank($id: String!, $institusiId: String!) {\n    setMainCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation setMainCompanyBank($id: String!, $institusiId: String!) {\n    setMainCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation deleteCompanyBank($id: String!, $institusiId: String!) {\n    deleteCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation deleteCompanyBank($id: String!, $institusiId: String!) {\n    deleteCompanyBank(id: $id, institusiId: $institusiId) {\n      ... on CompanyBankInfo {\n        id\n        companyId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query companyBankAccount($institusiId: String!) {\n    companyBankAccount(institusiId: $institusiId) {\n      ... on BankAccountList {\n        __typename\n        banks {\n          accountName\n          accountNo\n          bankCode\n          bankName\n          branchName\n          id\n          isMain\n          isVerify\n          status\n          createdAt\n          createdBy\n          updatedAt\n          updatedBy\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query companyBankAccount($institusiId: String!) {\n    companyBankAccount(institusiId: $institusiId) {\n      ... on BankAccountList {\n        __typename\n        banks {\n          accountName\n          accountNo\n          bankCode\n          bankName\n          branchName\n          id\n          isMain\n          isVerify\n          status\n          createdAt\n          createdBy\n          updatedAt\n          updatedBy\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation syncKBLI($input: SyncKBLIInput!) {\n    syncKBLI(input: $input) {\n      ... on SyncKBLIRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation syncKBLI($input: SyncKBLIInput!) {\n    syncKBLI(input: $input) {\n      ... on SyncKBLIRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation syncCompany($input: SyncCompanyInput!) {\n    syncCompany(input: $input) {\n      ... on SyncCompanyRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation syncCompany($input: SyncCompanyInput!) {\n    syncCompany(input: $input) {\n      ... on SyncCompanyRes {\n        institutionId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updatePenyediaRole($input: UpdatePenyediaInput!) {\n    updatePenyedia(input: $input) {\n      ... on UpdatePenyediaResult {\n        __typename\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updatePenyediaRole($input: UpdatePenyediaInput!) {\n    updatePenyedia(input: $input) {\n      ... on UpdatePenyediaResult {\n        __typename\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        institusi {\n          id\n          institutionType\n        }\n        application {\n          name\n        }\n        klpd {\n          jenisKlpd\n          namaKlpd\n        }\n        company {\n          bidangIndustri\n          bentukUsaha\n          name\n          nib\n          npwp\n          npwpToken\n          suratPKPToken\n          updatedAt\n        }\n        persona {\n          institusiId\n          appRole\n          appUserData {\n            pegGolongan\n            lpseId\n          }\n          appUserName\n          expiredAt\n          id\n          isPenyedia\n          noPPSDM\n          status\n          unit\n          createdAt\n          updatedAt\n          rejectReason {\n            items\n            reason\n          }\n          noPPSDMToken\n          noPPSDM\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n        }\n        satker {\n          alamat\n          kodeSatker\n          namaSatker\n          npwp\n          npwpValid\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        institusi {\n          id\n          institutionType\n        }\n        application {\n          name\n        }\n        klpd {\n          jenisKlpd\n          namaKlpd\n        }\n        company {\n          bidangIndustri\n          bentukUsaha\n          name\n          nib\n          npwp\n          npwpToken\n          suratPKPToken\n          updatedAt\n        }\n        persona {\n          institusiId\n          appRole\n          appUserData {\n            pegGolongan\n            lpseId\n          }\n          appUserName\n          expiredAt\n          id\n          isPenyedia\n          noPPSDM\n          status\n          unit\n          createdAt\n          updatedAt\n          rejectReason {\n            items\n            reason\n          }\n          noPPSDMToken\n          noPPSDM\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n        }\n        satker {\n          alamat\n          kodeSatker\n          namaSatker\n          npwp\n          npwpValid\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getPersonaPenyediaByID($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          aktaNotarisNo\n          beneficialOwnership\n          bentukUsaha\n          bidangIndustri\n          companyAddresses {\n            fullAddress\n          }\n          updatedAt\n          id\n          institusiId\n          isCabang\n          isPkp\n          isUkm\n          isUmkk\n          isNpwp16Valid\n          isNpwpValid\n          jenisPerusahaan\n          kswp\n          name\n          nib\n          noPkp\n          npwp\n          npwp16\n          oss {\n            project {\n              kbli {\n                kbli\n              }\n            }\n          }\n          picTtd\n          skalaUsaha\n          slug\n          status\n          telepon\n          umkType\n          username\n          website\n        }\n        penyediaFile {\n          nibUrl\n          npwpURL\n          suratPKPUrl\n          kswpURL\n        }\n        persona {\n          appRole\n          appUserName\n          id\n          institusiId\n          penyediaJabatan\n          status\n          rejectReason {\n            items\n            reason\n          }\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n          suratKuasaURL\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getPersonaPenyediaByID($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          aktaNotarisNo\n          beneficialOwnership\n          bentukUsaha\n          bidangIndustri\n          companyAddresses {\n            fullAddress\n          }\n          updatedAt\n          id\n          institusiId\n          isCabang\n          isPkp\n          isUkm\n          isUmkk\n          isNpwp16Valid\n          isNpwpValid\n          jenisPerusahaan\n          kswp\n          name\n          nib\n          noPkp\n          npwp\n          npwp16\n          oss {\n            project {\n              kbli {\n                kbli\n              }\n            }\n          }\n          picTtd\n          skalaUsaha\n          slug\n          status\n          telepon\n          umkType\n          username\n          website\n        }\n        penyediaFile {\n          nibUrl\n          npwpURL\n          suratPKPUrl\n          kswpURL\n        }\n        persona {\n          appRole\n          appUserName\n          id\n          institusiId\n          penyediaJabatan\n          status\n          rejectReason {\n            items\n            reason\n          }\n        }\n        personaFile {\n          suratKeteranganKerjaURL\n          suratKuasaURL\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getRoleCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          userId\n          penyediaJabatan\n        }\n        users {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getRoleCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          userId\n          penyediaJabatan\n        }\n        users {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getKBLIListV2(\n    $codes: [String]\n    $perPage: Int\n    $page: Int\n    $search: String\n  ) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page, search: $search) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n"): (typeof documents)["\n  query getKBLIListV2(\n    $codes: [String]\n    $perPage: Int\n    $page: Int\n    $search: String\n  ) {\n    getAllKBLI(codes: $codes, perPage: $perPage, page: $page, search: $search) {\n      currentPage\n      items {\n        code\n        id\n        title\n      }\n      lastPage\n      perPage\n      total\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation penyediaAdminTransfer($input: PenyediaAdminTransferInput!) {\n    penyediaAdminTransfer(input: $input) {\n      ... on PenyediaAdminTransferRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation penyediaAdminTransfer($input: PenyediaAdminTransferInput!) {\n    penyediaAdminTransfer(input: $input) {\n      ... on PenyediaAdminTransferRes {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation acceptCompanyMembership($input: AcceptCompanyMembershipInput!) {\n    acceptCompanyMembership(input: $input) {\n      ... on AcceptCompanyMembershipRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation acceptCompanyMembership($input: AcceptCompanyMembershipInput!) {\n    acceptCompanyMembership(input: $input) {\n      ... on AcceptCompanyMembershipRes {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation removeCompanyMembership($input: RemoveCompanyMembershipInput!) {\n    removeCompanyMembership(input: $input) {\n      ... on RemoveCompanyMembershipRes {\n        __typename\n        id\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation removeCompanyMembership($input: RemoveCompanyMembershipInput!) {\n    removeCompanyMembership(input: $input) {\n      ... on RemoveCompanyMembershipRes {\n        __typename\n        id\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getApplicationName {\n    getApplicationByFilter(filter: { isPilot: true }) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getApplicationName {\n    getApplicationByFilter(filter: { isPilot: true }) {\n      ... on ApplicationList {\n        __typename\n        applications {\n          name\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getAllPersonaPenyedia {\n    listPersonaPenyedia(filter: { statuses: PERSONA_VERIFIED }) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiMemberId\n          companyId\n          companyName\n          id\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getAllPersonaPenyedia {\n    listPersonaPenyedia(filter: { statuses: PERSONA_VERIFIED }) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          institusiMemberId\n          companyId\n          companyName\n          id\n          institusiId\n          role\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          suratKuasaToken\n          appId\n          suratKeteranganKerjaToken\n          userId\n          id\n          penyediaJabatan\n        }\n        users {\n          email\n          name\n          phone\n          status\n          profileStatus\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getCompanyMemberList($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        personas {\n          appRole\n          status\n          suratKuasaToken\n          appId\n          suratKeteranganKerjaToken\n          userId\n          id\n          penyediaJabatan\n        }\n        users {\n          email\n          name\n          phone\n          status\n          profileStatus\n          username\n          id\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query companyAdminTransferList($institutionId: String) {\n    companyAdminTransferList(institutionId: $institutionId) {\n      ... on RoleChangeList {\n        __typename\n        roleChanges {\n          id\n          institutionId\n          status\n          userIdTarget\n          userIdSource\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query companyAdminTransferList($institutionId: String) {\n    companyAdminTransferList(institutionId: $institutionId) {\n      ... on RoleChangeList {\n        __typename\n        roleChanges {\n          id\n          institutionId\n          status\n          userIdTarget\n          userIdSource\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updatePersonaShippingAddress(\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updatePersonaShippingAddress(id: $id, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updatePersonaShippingAddress(\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updatePersonaShippingAddress(id: $id, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateCompanyShippingAddress(\n    $companyId: String!\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updateCompanyShippingAddress(\n      companyId: $companyId\n      id: $id\n      input: $input\n    ) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        regionDetail {\n          cityName\n          districtName\n          provinceName\n          villageName\n        }\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateCompanyShippingAddress(\n    $companyId: String!\n    $id: String!\n    $input: AddressesTypeInput!\n  ) {\n    updateCompanyShippingAddress(\n      companyId: $companyId\n      id: $id\n      input: $input\n    ) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        regionDetail {\n          cityName\n          districtName\n          provinceName\n          villageName\n        }\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation deletePersonaShippingAddress($id: String!) {\n    deletePersonaShippingAddress(id: $id) {\n      ... on DeletePersonaShippingAddressResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation deletePersonaShippingAddress($id: String!) {\n    deletePersonaShippingAddress(id: $id) {\n      ... on DeletePersonaShippingAddressResult {\n        __typename\n        status\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation addPersonaShippingAddress(\n    $personaId: String!\n    $input: AddressesTypeInput!\n  ) {\n    addPersonaShippingAddress(personaId: $personaId, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation addPersonaShippingAddress(\n    $personaId: String!\n    $input: AddressesTypeInput!\n  ) {\n    addPersonaShippingAddress(personaId: $personaId, input: $input) {\n      ... on AddressesType {\n        id\n        fullAddress\n        isMainAddress\n        label\n        latitude\n        longitude\n        notes\n        phoneNumber\n        postalCode\n        receiverName\n        villageAreaCode\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getPersonaNonPenyediaShippingAddress($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        personaShippingAddress {\n          __typename\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getPersonaNonPenyediaShippingAddress($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        personaShippingAddress {\n          __typename\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getPersonaPenyediaShippingAddress($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          id\n        }\n        persona {\n          appRole\n        }\n        personaShippingAddress {\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getPersonaPenyediaShippingAddress($id: String!) {\n    getPersonaPenyediaByID(id: $id) {\n      ... on PersonaPenyedia {\n        __typename\n        penyedia {\n          id\n        }\n        persona {\n          appRole\n        }\n        personaShippingAddress {\n          fullAddress\n          id\n          isMainAddress\n          label\n          latitude\n          longitude\n          notes\n          phoneNumber\n          postalCode\n          receiverName\n          villageAreaCode\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getTraceParentRegion($areaCodes: [String!]) {\n    getTraceParentRegion(query: { areaCodes: $areaCodes }) {\n      items {\n        provinceAreaCode\n        provinceId\n        provinceName\n        cityAreaCode\n        cityId\n        cityName\n        districtAreaCode\n        districtId\n        districtName\n        villageAreaCode\n        villageId\n        villageName\n        villagePostalCode\n      }\n    }\n  }\n"): (typeof documents)["\n  query getTraceParentRegion($areaCodes: [String!]) {\n    getTraceParentRegion(query: { areaCodes: $areaCodes }) {\n      items {\n        provinceAreaCode\n        provinceId\n        provinceName\n        cityAreaCode\n        cityId\n        cityName\n        districtAreaCode\n        districtId\n        districtName\n        villageAreaCode\n        villageId\n        villageName\n        villagePostalCode\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getCompanyUserData($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        users {\n          id\n          email\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getCompanyUserData($institutionId: String!) {\n    getCompanyMemberList(institutionId: $institutionId) {\n      ... on CompanyMemberList {\n        __typename\n        users {\n          id\n          email\n          name\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getFinishedTicketCount(\n    $ticketCategory: TicketCategory!\n    $ticketId: String!\n  ) {\n    getFinishedTicketCount(\n      ticketCategory: $ticketCategory\n      ticketId: $ticketId\n    ) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getFinishedTicketCount(\n    $ticketCategory: TicketCategory!\n    $ticketId: String!\n  ) {\n    getFinishedTicketCount(\n      ticketCategory: $ticketCategory\n      ticketId: $ticketId\n    ) {\n      ... on GetFinishedTicketCountResult {\n        __typename\n        count\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getStatusPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        persona {\n          id\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getStatusPersonaNonPenyediaByID($id: String!) {\n    getPersonaNonpenyediaByID(id: $id) {\n      ... on PersonaNonPenyedia {\n        __typename\n        persona {\n          id\n          status\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getAccountData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        type\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getAccountData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        type\n        profileStatus\n        profile {\n          nik\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation requestOTP($tiketId: String!, $category: OTPCategory!) {\n    sendOTP(category: $category, tiketId: $tiketId) {\n      ... on SendOTPResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation requestOTP($tiketId: String!, $category: OTPCategory!) {\n    sendOTP(category: $category, tiketId: $tiketId) {\n      ... on SendOTPResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateEmail($tiketId: String!, $newEmail: String!) {\n    updateEmail(newEmail: $newEmail, tiketId: $tiketId) {\n      ... on UpdateEmailResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateEmail($tiketId: String!, $newEmail: String!) {\n    updateEmail(newEmail: $newEmail, tiketId: $tiketId) {\n      ... on UpdateEmailResult {\n        __typename\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation validateOTP(\n    $tiketId: String!\n    $otp: String!\n    $category: OTPCategory!\n  ) {\n    validateOTP(category: $category, otp: $otp, tiketId: $tiketId) {\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n      ... on ValidateOTPResult {\n        __typename\n        userId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation validateOTP(\n    $tiketId: String!\n    $otp: String!\n    $category: OTPCategory!\n  ) {\n    validateOTP(category: $category, otp: $otp, tiketId: $tiketId) {\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n      ... on ValidateOTPResult {\n        __typename\n        userId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getUserData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUserData {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation reuploadEnterpriseFile($input: ReuploadEnterpriseFileInput!) {\n    reuploadEnterpriseFile(input: $input) {\n      ... on ReuploadEnterpriseFileRes {\n        __typename\n        institusiId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation reuploadEnterpriseFile($input: ReuploadEnterpriseFileInput!) {\n    reuploadEnterpriseFile(input: $input) {\n      ... on ReuploadEnterpriseFileRes {\n        __typename\n        institusiId\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getInstitutionById($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          npwp\n          name\n          oss {\n            companyType\n          }\n          isNpwpValid\n          npwp16\n          npwp\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getInstitutionById($id: String!) {\n    getInstitutionByID(id: $id) {\n      ... on Institution {\n        __typename\n        company {\n          npwp\n          name\n          oss {\n            companyType\n          }\n          isNpwpValid\n          npwp16\n          npwp\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query downloadAccount($tokens: [String!]!) {\n    downloadAccount(tokens: $tokens) {\n      ... on DownloadAccount {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query downloadAccount($tokens: [String!]!) {\n    downloadAccount(tokens: $tokens) {\n      ... on DownloadAccount {\n        __typename\n        signedUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation uploadAccountPublicEnc($input: [UploadAccountPublicRequest]) {\n    uploadAccountPublic(input: $input) {\n      ... on UploadAccountPublicSuccess {\n        __typename\n        result {\n          identifier\n          publicKey\n          signedUrl\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation uploadAccountPublicEnc($input: [UploadAccountPublicRequest]) {\n    uploadAccountPublic(input: $input) {\n      ... on UploadAccountPublicSuccess {\n        __typename\n        result {\n          identifier\n          publicKey\n          signedUrl\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateNextOnboardingStep {\n    updateNextOnboardingStep {\n      ... on GetOnboardingStatus {\n        __typename\n        nextStep\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateNextOnboardingStep {\n    updateNextOnboardingStep {\n      ... on GetOnboardingStatus {\n        __typename\n        nextStep\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation setContractAuthCodeAccount($input: SetAuthCodeRequest) {\n    setContractAuthCodeAccount(input: $input) {\n      id\n      userId\n      provider\n      providerUserId\n      providerUserName\n      providerUserEmail\n      providerUserPhone\n      providerUserNik\n      status\n      signToken\n      rejectReason\n      updatedAt\n    }\n  }\n"): (typeof documents)["\n  mutation setContractAuthCodeAccount($input: SetAuthCodeRequest) {\n    setContractAuthCodeAccount(input: $input) {\n      id\n      userId\n      provider\n      providerUserId\n      providerUserName\n      providerUserEmail\n      providerUserPhone\n      providerUserNik\n      status\n      signToken\n      rejectReason\n      updatedAt\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateVerificationProcess($input: UpdateVerificationProcessInput!) {\n    updateVerificationProcess(input: $input) {\n      code\n      message\n      reqId\n      __typename\n    }\n  }\n"): (typeof documents)["\n  mutation updateVerificationProcess($input: UpdateVerificationProcessInput!) {\n    updateVerificationProcess(input: $input) {\n      code\n      message\n      reqId\n      __typename\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getStatusOnboarding {\n    getStatusOnboarding {\n      ... on StatusOnBoardingInfo {\n        __typename\n        currentStep\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  query getStatusOnboarding {\n    getStatusOnboarding {\n      ... on StatusOnBoardingInfo {\n        __typename\n        currentStep\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getInterviewSchedule(\n    $personaId: String\n    $type: VerificationType! = USER\n  ) {\n    getInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  query getInterviewSchedule(\n    $personaId: String\n    $type: VerificationType! = USER\n  ) {\n    getInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query generateInterviewSchedule(\n    $type: VerificationType! = USER\n    $personaId: String\n  ) {\n    generateInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query generateInterviewSchedule(\n    $type: VerificationType! = USER\n    $personaId: String\n  ) {\n    generateInterviewSchedule(type: $type, personaId: $personaId) {\n      ... on InterviewSchedule {\n        __typename\n        endTime\n        startTime\n        url\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query privyRegLinkAccount {\n    privyRegLinkAccount {\n      ... on PrivyRegLink {\n        __typename\n        registrationUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query privyRegLinkAccount {\n    privyRegLinkAccount {\n      ... on PrivyRegLink {\n        __typename\n        registrationUrl\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getPersonaListForPersonaChooser($appId: String!) {\n    getPersonaListForPersonaChooser(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          appRole\n          companyName\n          isPenyedia\n          klpdName\n          personaId\n          satkerKode\n          satkerName\n          status\n          tokenClaim {\n            institutionId\n            institutionType\n            isTest\n            legacyAppsUserId\n            legacyAppsUserIdStr\n            legacyAppsUsername\n            personaId\n            personaOwner\n            role\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getPersonaListForPersonaChooser($appId: String!) {\n    getPersonaListForPersonaChooser(appId: $appId) {\n      ... on GetPersonaListForApplicationResult {\n        __typename\n        persona {\n          appRole\n          companyName\n          isPenyedia\n          klpdName\n          personaId\n          satkerKode\n          satkerName\n          status\n          tokenClaim {\n            institutionId\n            institutionType\n            isTest\n            legacyAppsUserId\n            legacyAppsUserIdStr\n            legacyAppsUsername\n            personaId\n            personaOwner\n            role\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getAccountUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        type\n        isSeller\n        username\n        status\n        profileStatus\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getAccountUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        type\n        isSeller\n        username\n        status\n        profileStatus\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getUserType {\n    me {\n      ... on UserInfo {\n        __typename\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUserType {\n    me {\n      ... on UserInfo {\n        __typename\n        type\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getUserNIP {\n    me {\n      ... on UserInfo {\n        __typename\n        profile {\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUserNIP {\n    me {\n      ... on UserInfo {\n        __typename\n        profile {\n          nip\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation uploadAccountEncProcess($input: [UploadAccountEncProcessRequest]) {\n    uploadAccountEncProcess(input: $input) {\n      ... on UploadAccountEncProcessResult {\n        __typename\n        result {\n          identifier\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation uploadAccountEncProcess($input: [UploadAccountEncProcessRequest]) {\n    uploadAccountEncProcess(input: $input) {\n      ... on UploadAccountEncProcessResult {\n        __typename\n        result {\n          identifier\n          token\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getListPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          appId\n          id\n          companyId\n          companyName\n          institusiId\n          institusiMemberId\n          klpdName\n          role\n          satkerKode\n          satkerName\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getListPersonaNonPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaNonPenyedia(filter: $filter) {\n      ... on ListPersonaNonPenyedia {\n        __typename\n        persona {\n          appId\n          id\n          companyId\n          companyName\n          institusiId\n          institusiMemberId\n          klpdName\n          role\n          satkerKode\n          satkerName\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getListPersonaPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          appId\n          companyId\n          companyName\n          id\n          institusiId\n          institusiMemberId\n          role\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getListPersonaPenyedia($filter: ListPersonaFilterInput!) {\n    listPersonaPenyedia(filter: $filter) {\n      ... on ListPersonaPenyedia {\n        __typename\n        persona {\n          appId\n          companyId\n          companyName\n          id\n          institusiId\n          institusiMemberId\n          role\n          userRoleId\n          status\n          updatedAt\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query uploadAccountEnc {\n    uploadAccountEnc {\n      ... on UploadAccountEncResult {\n        __typename\n        key\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query uploadAccountEnc {\n    uploadAccountEnc {\n      ... on UploadAccountEncResult {\n        __typename\n        key\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getSubscriberHash {\n    subscriberHash {\n      subscriberHash\n      subscriberId\n    }\n  }\n"): (typeof documents)["\n  query getSubscriberHash {\n    subscriberHash {\n      subscriberHash\n      subscriberId\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;