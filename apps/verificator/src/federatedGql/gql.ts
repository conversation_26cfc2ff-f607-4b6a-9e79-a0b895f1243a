/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation startVerification($input: StartVerificationInput) {\n    startVerification(input: $input) {\n      ... on StartVerificationResult {\n        __typename\n        personaId\n        provider\n        status\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.StartVerificationDocument,
    "\n  mutation updateSatker($input: UpdateSatkerInput!) {\n    updateSatker(input: $input) {\n      ... on UpdateSatkerResult {\n        __typename\n        alamat\n        npwp\n        kodeSatkerAdj\n        namaSatker\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.UpdateSatkerDocument,
    "\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CheckSatkerNpwpValidityDocument,
    "\n  mutation acceptVerification($input: AcceptVerificationInput!) {\n    acceptVerification(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on AcceptVerificationSuccess {\n        personaId\n        userId\n        status\n        __typename\n      }\n    }\n  }\n": typeof types.AcceptVerificationDocument,
    "\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.UploadAccountDocument,
    "\n  mutation updateProfileData($input: UpdateProfileData!) {\n    updateProfileData(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on UpdateProfileDataResult {\n        __typename\n        userId\n        personaId\n        nip\n        nik\n        name\n        ktpToken\n        dateOfBirth\n      }\n    }\n  }\n": typeof types.UpdateProfileDataDocument,
    "\n  mutation holdVerification($input: HoldVerificationInput!) {\n    holdVerification(input: $input) {\n      ... on HoldVerificationResult {\n        __typename\n        pendingReason\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.HoldVerificationDocument,
    "\n  mutation rejectVerification($input: RejectVerificationInput!) {\n    rejectVerification(input: $input) {\n      ... on RejectVerificationSuccess {\n        __typename\n        personaId\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n": typeof types.RejectVerificationDocument,
    "\n  query listNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.ListNonPenyediaVerificationDocument,
    "\n  query countNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        data {\n          nonPenyedia {\n            name\n          }\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.CountNonPenyediaVerificationDocument,
    "\n  query personaStatusNonPenyediaVerification($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            status\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.PersonaStatusNonPenyediaVerificationDocument,
    "\n  query detailVerificationNonPenyediaPersona($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            appUserName\n            appUserData {\n              legacyUsername\n              pegGolongan\n            }\n            status\n            appRole\n            createdAt\n            expiredAt\n            updatedAt\n            noPPSDM\n            noPPSDMToken\n            unit\n            pengaktifanRUP\n            verifikasiPersona\n            blu\n            penyediaJabatan\n            userId\n            id\n            suratKeteranganKerjaToken\n            rejectReason {\n              items\n              reason\n            }\n            provider\n          }\n          satker {\n            namaSatker\n            npwp\n            namaKlpd\n            kodeKlpd\n            alamat\n            kodeSatkerAdj\n            npwpValid\n          }\n          klpd {\n            namaKlpd\n          }\n          application {\n            name\n            id\n          }\n          institusi {\n            id\n          }\n        }\n        user {\n          name\n          nik\n          dateOfBirth\n          email\n          phoneNumber\n          nip\n          profileStatus\n          username\n          privyIdStatus\n          bsreStatus\n          privyId\n          provider\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.DetailVerificationNonPenyediaPersonaDocument,
    "\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n          jenisKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": typeof types.KlpdDocument,
    "\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.ReviewAccessPersonaDocument,
    "\n  query useGetListReaktivasiAkses(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.UseGetListReaktivasiAksesDocument,
    "\n  query detailReviewAccessPersona($personaId: String!) {\n    detailReviewAccessPersona(personaId: $personaId) {\n      ... on DetailReviewAccessPersonaResult {\n        suratKeteranganKerjaToken\n        rejectReason {\n          items\n          reason\n        }\n        status\n        expiredAt\n        createdAt\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.DetailReviewAccessPersonaDocument,
    "\n  query getUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        email\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetUserInfoDocument,
    "\n  query getInstitutionById($institutionId: String!) {\n    getInstitutionByID(id: $institutionId) {\n      ... on Institution {\n        __typename\n        satker {\n          kodeSatker\n          kodeKlpd\n        }\n        klpd {\n          kodeKlpd\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetInstitutionByIdDocument,
    "\n  query getListSatkerByKlpd(\n    $pagination: AccountPagination!\n    $filter: SatkerFilter!\n  ) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          institutionId\n          namaSatker\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.GetListSatkerByKlpdDocument,
};
const documents: Documents = {
    "\n  mutation startVerification($input: StartVerificationInput) {\n    startVerification(input: $input) {\n      ... on StartVerificationResult {\n        __typename\n        personaId\n        provider\n        status\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.StartVerificationDocument,
    "\n  mutation updateSatker($input: UpdateSatkerInput!) {\n    updateSatker(input: $input) {\n      ... on UpdateSatkerResult {\n        __typename\n        alamat\n        npwp\n        kodeSatkerAdj\n        namaSatker\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.UpdateSatkerDocument,
    "\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CheckSatkerNpwpValidityDocument,
    "\n  mutation acceptVerification($input: AcceptVerificationInput!) {\n    acceptVerification(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on AcceptVerificationSuccess {\n        personaId\n        userId\n        status\n        __typename\n      }\n    }\n  }\n": types.AcceptVerificationDocument,
    "\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.UploadAccountDocument,
    "\n  mutation updateProfileData($input: UpdateProfileData!) {\n    updateProfileData(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on UpdateProfileDataResult {\n        __typename\n        userId\n        personaId\n        nip\n        nik\n        name\n        ktpToken\n        dateOfBirth\n      }\n    }\n  }\n": types.UpdateProfileDataDocument,
    "\n  mutation holdVerification($input: HoldVerificationInput!) {\n    holdVerification(input: $input) {\n      ... on HoldVerificationResult {\n        __typename\n        pendingReason\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n": types.HoldVerificationDocument,
    "\n  mutation rejectVerification($input: RejectVerificationInput!) {\n    rejectVerification(input: $input) {\n      ... on RejectVerificationSuccess {\n        __typename\n        personaId\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n": types.RejectVerificationDocument,
    "\n  query listNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.ListNonPenyediaVerificationDocument,
    "\n  query countNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        data {\n          nonPenyedia {\n            name\n          }\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.CountNonPenyediaVerificationDocument,
    "\n  query personaStatusNonPenyediaVerification($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            status\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.PersonaStatusNonPenyediaVerificationDocument,
    "\n  query detailVerificationNonPenyediaPersona($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            appUserName\n            appUserData {\n              legacyUsername\n              pegGolongan\n            }\n            status\n            appRole\n            createdAt\n            expiredAt\n            updatedAt\n            noPPSDM\n            noPPSDMToken\n            unit\n            pengaktifanRUP\n            verifikasiPersona\n            blu\n            penyediaJabatan\n            userId\n            id\n            suratKeteranganKerjaToken\n            rejectReason {\n              items\n              reason\n            }\n            provider\n          }\n          satker {\n            namaSatker\n            npwp\n            namaKlpd\n            kodeKlpd\n            alamat\n            kodeSatkerAdj\n            npwpValid\n          }\n          klpd {\n            namaKlpd\n          }\n          application {\n            name\n            id\n          }\n          institusi {\n            id\n          }\n        }\n        user {\n          name\n          nik\n          dateOfBirth\n          email\n          phoneNumber\n          nip\n          profileStatus\n          username\n          privyIdStatus\n          bsreStatus\n          privyId\n          provider\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.DetailVerificationNonPenyediaPersonaDocument,
    "\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n          jenisKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n": types.KlpdDocument,
    "\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.ReviewAccessPersonaDocument,
    "\n  query useGetListReaktivasiAkses(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.UseGetListReaktivasiAksesDocument,
    "\n  query detailReviewAccessPersona($personaId: String!) {\n    detailReviewAccessPersona(personaId: $personaId) {\n      ... on DetailReviewAccessPersonaResult {\n        suratKeteranganKerjaToken\n        rejectReason {\n          items\n          reason\n        }\n        status\n        expiredAt\n        createdAt\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.DetailReviewAccessPersonaDocument,
    "\n  query getUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        email\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetUserInfoDocument,
    "\n  query getInstitutionById($institutionId: String!) {\n    getInstitutionByID(id: $institutionId) {\n      ... on Institution {\n        __typename\n        satker {\n          kodeSatker\n          kodeKlpd\n        }\n        klpd {\n          kodeKlpd\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetInstitutionByIdDocument,
    "\n  query getListSatkerByKlpd(\n    $pagination: AccountPagination!\n    $filter: SatkerFilter!\n  ) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          institutionId\n          namaSatker\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.GetListSatkerByKlpdDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation startVerification($input: StartVerificationInput) {\n    startVerification(input: $input) {\n      ... on StartVerificationResult {\n        __typename\n        personaId\n        provider\n        status\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation startVerification($input: StartVerificationInput) {\n    startVerification(input: $input) {\n      ... on StartVerificationResult {\n        __typename\n        personaId\n        provider\n        status\n        userId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateSatker($input: UpdateSatkerInput!) {\n    updateSatker(input: $input) {\n      ... on UpdateSatkerResult {\n        __typename\n        alamat\n        npwp\n        kodeSatkerAdj\n        namaSatker\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateSatker($input: UpdateSatkerInput!) {\n    updateSatker(input: $input) {\n      ... on UpdateSatkerResult {\n        __typename\n        alamat\n        npwp\n        kodeSatkerAdj\n        namaSatker\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation checkSatkerNPWPValidity($input: CheckSatkerNPWPValidityInput!) {\n    checkSatkerNPWPValidity(input: $input) {\n      ... on CheckSatkerNPWPValidityResult {\n        __typename\n        valid\n        address\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation acceptVerification($input: AcceptVerificationInput!) {\n    acceptVerification(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on AcceptVerificationSuccess {\n        personaId\n        userId\n        status\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation acceptVerification($input: AcceptVerificationInput!) {\n    acceptVerification(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on AcceptVerificationSuccess {\n        personaId\n        userId\n        status\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation uploadAccount($input: [UploadAccountRequest]) {\n    uploadAccount(input: $input) {\n      ... on UploadAccountSuccess {\n        __typename\n        result {\n          __typename\n          identifier\n          token\n          signedUrl\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateProfileData($input: UpdateProfileData!) {\n    updateProfileData(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on UpdateProfileDataResult {\n        __typename\n        userId\n        personaId\n        nip\n        nik\n        name\n        ktpToken\n        dateOfBirth\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateProfileData($input: UpdateProfileData!) {\n    updateProfileData(input: $input) {\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n      ... on UpdateProfileDataResult {\n        __typename\n        userId\n        personaId\n        nip\n        nik\n        name\n        ktpToken\n        dateOfBirth\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation holdVerification($input: HoldVerificationInput!) {\n    holdVerification(input: $input) {\n      ... on HoldVerificationResult {\n        __typename\n        pendingReason\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation holdVerification($input: HoldVerificationInput!) {\n    holdVerification(input: $input) {\n      ... on HoldVerificationResult {\n        __typename\n        pendingReason\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation rejectVerification($input: RejectVerificationInput!) {\n    rejectVerification(input: $input) {\n      ... on RejectVerificationSuccess {\n        __typename\n        personaId\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation rejectVerification($input: RejectVerificationInput!) {\n    rejectVerification(input: $input) {\n      ... on RejectVerificationSuccess {\n        __typename\n        personaId\n        userId\n      }\n      ... on GenericError {\n        __typename\n        message\n        reqId\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query listNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query listNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query countNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        data {\n          nonPenyedia {\n            name\n          }\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query countNonPenyediaVerification(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        data {\n          nonPenyedia {\n            name\n          }\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query personaStatusNonPenyediaVerification($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            status\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query personaStatusNonPenyediaVerification($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            status\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query detailVerificationNonPenyediaPersona($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            appUserName\n            appUserData {\n              legacyUsername\n              pegGolongan\n            }\n            status\n            appRole\n            createdAt\n            expiredAt\n            updatedAt\n            noPPSDM\n            noPPSDMToken\n            unit\n            pengaktifanRUP\n            verifikasiPersona\n            blu\n            penyediaJabatan\n            userId\n            id\n            suratKeteranganKerjaToken\n            rejectReason {\n              items\n              reason\n            }\n            provider\n          }\n          satker {\n            namaSatker\n            npwp\n            namaKlpd\n            kodeKlpd\n            alamat\n            kodeSatkerAdj\n            npwpValid\n          }\n          klpd {\n            namaKlpd\n          }\n          application {\n            name\n            id\n          }\n          institusi {\n            id\n          }\n        }\n        user {\n          name\n          nik\n          dateOfBirth\n          email\n          phoneNumber\n          nip\n          profileStatus\n          username\n          privyIdStatus\n          bsreStatus\n          privyId\n          provider\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query detailVerificationNonPenyediaPersona($personaId: String!) {\n    detailVerificationNonPenyediaPersona(personaId: $personaId) {\n      ... on DetailVerificationNonPenyediaPersonaResult {\n        __typename\n        persona {\n          persona {\n            appUserName\n            appUserData {\n              legacyUsername\n              pegGolongan\n            }\n            status\n            appRole\n            createdAt\n            expiredAt\n            updatedAt\n            noPPSDM\n            noPPSDMToken\n            unit\n            pengaktifanRUP\n            verifikasiPersona\n            blu\n            penyediaJabatan\n            userId\n            id\n            suratKeteranganKerjaToken\n            rejectReason {\n              items\n              reason\n            }\n            provider\n          }\n          satker {\n            namaSatker\n            npwp\n            namaKlpd\n            kodeKlpd\n            alamat\n            kodeSatkerAdj\n            npwpValid\n          }\n          klpd {\n            namaKlpd\n          }\n          application {\n            name\n            id\n          }\n          institusi {\n            id\n          }\n        }\n        user {\n          name\n          nik\n          dateOfBirth\n          email\n          phoneNumber\n          nip\n          profileStatus\n          username\n          privyIdStatus\n          bsreStatus\n          privyId\n          provider\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n          jenisKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  query klpd($filter: KlpdFilter, $pagination: AccountPagination!) {\n    klpd(filter: $filter, pagination: $pagination) {\n      ... on KlpdResult {\n        __typename\n        paginationInfo {\n          currentPage\n          total\n        }\n        items {\n          kodeKlpd\n          namaKlpd\n          jenisKlpd\n        }\n        __typename\n      }\n      ... on GenericError {\n        reqId\n        message\n        code\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation reviewAccessPersona($input: ReviewAccessPersonaInput!) {\n    reviewAccessPersona(input: $input) {\n      ... on ReviewAccessPersonaResult {\n        __typename\n        status\n        personaId\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query useGetListReaktivasiAkses(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  query useGetListReaktivasiAkses(\n    $filter: ListPersonaVerificationFilter\n    $pagination: AccountPagination!\n  ) {\n    listPersonaVerification(pagination: $pagination, filter: $filter) {\n      ... on ListPersonaVerificationResult {\n        __typename\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n        data {\n          nonPenyedia {\n            status\n            name\n            namaSatker\n            namaKlpd\n            createdAt\n            personaId\n            provider\n            role\n            jenisInstansi\n          }\n        }\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query detailReviewAccessPersona($personaId: String!) {\n    detailReviewAccessPersona(personaId: $personaId) {\n      ... on DetailReviewAccessPersonaResult {\n        suratKeteranganKerjaToken\n        rejectReason {\n          items\n          reason\n        }\n        status\n        expiredAt\n        createdAt\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query detailReviewAccessPersona($personaId: String!) {\n    detailReviewAccessPersona(personaId: $personaId) {\n      ... on DetailReviewAccessPersonaResult {\n        suratKeteranganKerjaToken\n        rejectReason {\n          items\n          reason\n        }\n        status\n        expiredAt\n        createdAt\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        email\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUserInfo {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        name\n        email\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getInstitutionById($institutionId: String!) {\n    getInstitutionByID(id: $institutionId) {\n      ... on Institution {\n        __typename\n        satker {\n          kodeSatker\n          kodeKlpd\n        }\n        klpd {\n          kodeKlpd\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getInstitutionById($institutionId: String!) {\n    getInstitutionByID(id: $institutionId) {\n      ... on Institution {\n        __typename\n        satker {\n          kodeSatker\n          kodeKlpd\n        }\n        klpd {\n          kodeKlpd\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getListSatkerByKlpd(\n    $pagination: AccountPagination!\n    $filter: SatkerFilter!\n  ) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          institutionId\n          namaSatker\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  query getListSatkerByKlpd(\n    $pagination: AccountPagination!\n    $filter: SatkerFilter!\n  ) {\n    satker(pagination: $pagination, filter: $filter) {\n      ... on SatkerResult {\n        __typename\n        items {\n          institutionId\n          namaSatker\n        }\n        paginationInfo {\n          currentPage\n          lastPage\n          nextPage\n          perPage\n          prevPage\n          total\n        }\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;