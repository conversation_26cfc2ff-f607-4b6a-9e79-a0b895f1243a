/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation DeleteDraft($input: DeleteBlacklistInput!) {\n    deleteBlacklist(input: $input) {\n      ... on DeleteBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.DeleteDraftDocument,
    "\n  query GetBlacklists($input: BlacklistsInput) {\n    blacklists(input: $input) {\n      blacklists {\n        id\n        skNumber\n        skNumberStatusBased\n        status\n        startDate\n        expiredDate\n        publishDate\n        publishDurationInMinutes\n        status\n        statusUpdatedAt\n        tender {\n          id\n          name\n          packageId\n          pagu\n          hps\n          budgetYear\n          category\n        }\n        provider {\n          id\n          name\n          npwp\n          address\n          additionalAddress\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n        }\n        correspondence {\n          lpse {\n            id\n            name\n          }\n          kldi {\n            name\n            id\n          }\n          satker {\n            id\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          month\n          year\n        }\n      }\n      pagination {\n        pageNumber\n        totalPage\n        totalData\n      }\n    }\n  }\n": typeof types.GetBlacklistsDocument,
    "\n  query SearchProvider($input: SearchProviderInput!) {\n    searchProvider(input: $input) {\n      id\n      source\n      name\n      npwp\n      nib\n      address\n    }\n  }\n": typeof types.SearchProviderDocument,
    "\n  query BlacklistsByProvider($id: String!, $source: ProviderSource = account) {\n    blacklistsByProvider(id: $id, source: $source) {\n      totalSanctionDurationInMinutes\n      blacklists {\n        id\n        skNumberStatusBased\n        publishDurationInMinutes\n        correspondence {\n          satker {\n            name\n            id\n          }\n        }\n      }\n    }\n  }\n": typeof types.BlacklistsByProviderDocument,
    "\n  query BlacklistHistory($id: ID!) {\n    blacklistDetail(id: $id) {\n      history {\n        status\n        skFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        supportFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        skNumber\n        id\n        description\n        createdAt\n      }\n    }\n  }\n": typeof types.BlacklistHistoryDocument,
    "\n  query BlacklistFormData(\n    $blacklistId: ID!\n    $syncSupplierData: Boolean\n    $syncUserData: Boolean\n  ) {\n    blacklistDetail(\n      id: $blacklistId\n      syncSupplierData: $syncSupplierData\n      syncUserData: $syncUserData\n    ) {\n      ... on Blacklist {\n        status\n        publishDate\n        #step 1\n        skNumber\n        document {\n          id\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        provider {\n          id\n          name\n          npwp\n          nib\n          additionalAddress\n          nibSource\n          address\n          regency {\n            name\n          }\n          province {\n            name\n          }\n          source\n        }\n        violation {\n          id\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n          }\n        }\n        tender {\n          id\n          category\n          packageId\n          name\n          satker {\n            name\n          }\n          pagu\n          hps\n          budgetYear\n          kldi {\n            name\n          }\n          source\n        }\n        startDate\n        expiredDate\n        #step 2\n        skAdditional {\n          instance {\n            logoFileToken\n            logoFileUrl\n            name\n            address\n          }\n          additionalDocumentData {\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n            pokja {\n              letterNumber\n              letterDate\n            }\n          }\n          officialPosition\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n    activeViolations {\n      ... on Violation {\n        id\n        name\n        description\n        month\n        year\n      }\n    }\n    lpseList {\n      ... on Lpse {\n        id\n        name\n      }\n    }\n  }\n": typeof types.BlacklistFormDataDocument,
    "\n  mutation UploadFile($fileName: String!, $contentType: String!) {\n    createSignedUrl(\n      input: {\n        contentType: $contentType\n        identifier: $fileName\n        fileName: $fileName\n        isPublic: true\n      }\n    ) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UploadFileDocument,
    "\n  mutation UploadFileFull($input: CreateSignedUrlInput!) {\n    createSignedUrl(input: $input) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UploadFileFullDocument,
    "\n  mutation CreateBlacklist($input: CreateBlacklistInput!) {\n    createBlacklist(input: $input) {\n      ... on CreateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        code\n        message\n        reqId\n        __typename\n      }\n    }\n  }\n": typeof types.CreateBlacklistDocument,
    "\n  mutation UpdateBlacklist($input: UpdateBlacklistInput!) {\n    updateBlacklist(input: $input) {\n      ... on UpdateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UpdateBlacklistDocument,
    "\n  mutation CreateDraft($input: CreateDraftBlacklistInput!) {\n    createDraftBlacklist(input: $input) {\n      ... on CreateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.CreateDraftDocument,
    "\n  mutation UpdateDraft($input: UpdateDraftBlacklistInput!) {\n    updateDraftBlacklist(input: $input) {\n      ... on UpdateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UpdateDraftDocument,
    "\n  query ValidateTenderOwner($input: ValidateTenderOwnerInput!) {\n    validateTenderOwner(input: $input) {\n      isOwner\n      reason\n    }\n  }\n": typeof types.ValidateTenderOwnerDocument,
    "\n  query GetTenderDetail($input: TenderInput!) {\n    tenderDetail(input: $input) {\n      id\n      packageId\n      name\n      category\n      satker {\n        name\n      }\n      pagu\n      hps\n      budgetYear\n      kldi {\n        name\n      }\n      source\n    }\n  }\n": typeof types.GetTenderDetailDocument,
    "\n  query AddFormRequiredData(\n    $providerId: String!\n    $source: ProviderSource = account\n  ) {\n    providerDetail(id: $providerId, source: $source) {\n      id\n      name\n      npwp\n      nib\n      additionalAddress\n      nibSource\n      address\n      regency {\n        name\n      }\n      province {\n        name\n      }\n      source\n    }\n    activeViolations {\n      id\n      name\n      description\n      month\n      year\n    }\n    lpseList {\n      id\n      name\n    }\n  }\n": typeof types.AddFormRequiredDataDocument,
    "\n  mutation CreateAddendum($input: CreateBlacklistAddendumInput!) {\n    createBlacklistAddendum(input: $input) {\n      ... on CreateBlacklistAddendumResult {\n        __typename\n        blacklistID\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": typeof types.CreateAddendumDocument,
    "\n  query SearchAreaInput($input: SearchAreaInput!) {\n    searchArea(input: $input) {\n      ... on City {\n        id\n        name\n      }\n    }\n  }\n": typeof types.SearchAreaInputDocument,
    "\n  mutation CreateDownloadSignedUrl($input: CreateDownloadSignedUrlInput!) {\n    createDownloadSignedUrl(input: $input) {\n      ... on CreateDownloadSignedUrlResult {\n        __typename\n        expiry\n        token\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.CreateDownloadSignedUrlDocument,
    "\n  mutation eSign($input: ESignInput!) {\n    eSign(input: $input) {\n      ... on ESignResult {\n        __typename\n        documentToken\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.ESignDocument,
    "\n  mutation UpdateBlacklistStatus($input: UpdateBlacklistStatusInput!) {\n    updateBlacklistStatus(input: $input) {\n      ... on UpdateBlacklistStatusResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": typeof types.UpdateBlacklistStatusDocument,
    "\n  query blackListDetail($id: ID!) {\n    completeBlacklistDetail(id: $id) {\n      ... on CompleteBlacklist {\n        id\n        skNumber\n        skNumberCanceled\n        skNumberCanceledTemporary\n        skNumberInitial\n        startDate\n        expiredDate\n        canceledDate\n        canceledTemporaryDate\n        status\n        provider {\n          id\n          source\n          providerId\n          name\n          npwp\n          nib\n          nibSource\n          address\n          additionalAddress\n          regency {\n            name\n          }\n          province {\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          year\n          month\n        }\n        tender {\n          id\n          name\n          packageId\n          budgetYear\n          hps\n          pagu\n          source\n          budgetYear\n          packageId\n          kldi {\n            id\n            name\n          }\n          satker {\n            id\n            name\n            address\n          }\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              size\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        history {\n          id\n          skNumber\n          status\n          description\n          createdAt\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n            name\n          }\n          kldi {\n            id\n            name\n          }\n        }\n        skAdditional {\n          id\n          officialPosition\n          instance {\n            logoFileToken\n            name\n            address\n            logoFileUrl\n          }\n          additionalDocumentData {\n            pokja {\n              letterNumber\n              letterDate\n            }\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n          }\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n  }\n": typeof types.BlackListDetailDocument,
    "\n  query getBlacklistUser {\n    user {\n      ... on PaKpa {\n        id\n        name\n        email\n        phone\n        nip\n        kldi {\n          id\n          name\n        }\n        satker {\n          id\n          name\n          address\n        }\n      }\n    }\n  }\n": typeof types.GetBlacklistUserDocument,
    "\n  query getUser {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        name\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": typeof types.GetUserDocument,
};
const documents: Documents = {
    "\n  mutation DeleteDraft($input: DeleteBlacklistInput!) {\n    deleteBlacklist(input: $input) {\n      ... on DeleteBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.DeleteDraftDocument,
    "\n  query GetBlacklists($input: BlacklistsInput) {\n    blacklists(input: $input) {\n      blacklists {\n        id\n        skNumber\n        skNumberStatusBased\n        status\n        startDate\n        expiredDate\n        publishDate\n        publishDurationInMinutes\n        status\n        statusUpdatedAt\n        tender {\n          id\n          name\n          packageId\n          pagu\n          hps\n          budgetYear\n          category\n        }\n        provider {\n          id\n          name\n          npwp\n          address\n          additionalAddress\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n        }\n        correspondence {\n          lpse {\n            id\n            name\n          }\n          kldi {\n            name\n            id\n          }\n          satker {\n            id\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          month\n          year\n        }\n      }\n      pagination {\n        pageNumber\n        totalPage\n        totalData\n      }\n    }\n  }\n": types.GetBlacklistsDocument,
    "\n  query SearchProvider($input: SearchProviderInput!) {\n    searchProvider(input: $input) {\n      id\n      source\n      name\n      npwp\n      nib\n      address\n    }\n  }\n": types.SearchProviderDocument,
    "\n  query BlacklistsByProvider($id: String!, $source: ProviderSource = account) {\n    blacklistsByProvider(id: $id, source: $source) {\n      totalSanctionDurationInMinutes\n      blacklists {\n        id\n        skNumberStatusBased\n        publishDurationInMinutes\n        correspondence {\n          satker {\n            name\n            id\n          }\n        }\n      }\n    }\n  }\n": types.BlacklistsByProviderDocument,
    "\n  query BlacklistHistory($id: ID!) {\n    blacklistDetail(id: $id) {\n      history {\n        status\n        skFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        supportFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        skNumber\n        id\n        description\n        createdAt\n      }\n    }\n  }\n": types.BlacklistHistoryDocument,
    "\n  query BlacklistFormData(\n    $blacklistId: ID!\n    $syncSupplierData: Boolean\n    $syncUserData: Boolean\n  ) {\n    blacklistDetail(\n      id: $blacklistId\n      syncSupplierData: $syncSupplierData\n      syncUserData: $syncUserData\n    ) {\n      ... on Blacklist {\n        status\n        publishDate\n        #step 1\n        skNumber\n        document {\n          id\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        provider {\n          id\n          name\n          npwp\n          nib\n          additionalAddress\n          nibSource\n          address\n          regency {\n            name\n          }\n          province {\n            name\n          }\n          source\n        }\n        violation {\n          id\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n          }\n        }\n        tender {\n          id\n          category\n          packageId\n          name\n          satker {\n            name\n          }\n          pagu\n          hps\n          budgetYear\n          kldi {\n            name\n          }\n          source\n        }\n        startDate\n        expiredDate\n        #step 2\n        skAdditional {\n          instance {\n            logoFileToken\n            logoFileUrl\n            name\n            address\n          }\n          additionalDocumentData {\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n            pokja {\n              letterNumber\n              letterDate\n            }\n          }\n          officialPosition\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n    activeViolations {\n      ... on Violation {\n        id\n        name\n        description\n        month\n        year\n      }\n    }\n    lpseList {\n      ... on Lpse {\n        id\n        name\n      }\n    }\n  }\n": types.BlacklistFormDataDocument,
    "\n  mutation UploadFile($fileName: String!, $contentType: String!) {\n    createSignedUrl(\n      input: {\n        contentType: $contentType\n        identifier: $fileName\n        fileName: $fileName\n        isPublic: true\n      }\n    ) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UploadFileDocument,
    "\n  mutation UploadFileFull($input: CreateSignedUrlInput!) {\n    createSignedUrl(input: $input) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UploadFileFullDocument,
    "\n  mutation CreateBlacklist($input: CreateBlacklistInput!) {\n    createBlacklist(input: $input) {\n      ... on CreateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        code\n        message\n        reqId\n        __typename\n      }\n    }\n  }\n": types.CreateBlacklistDocument,
    "\n  mutation UpdateBlacklist($input: UpdateBlacklistInput!) {\n    updateBlacklist(input: $input) {\n      ... on UpdateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UpdateBlacklistDocument,
    "\n  mutation CreateDraft($input: CreateDraftBlacklistInput!) {\n    createDraftBlacklist(input: $input) {\n      ... on CreateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.CreateDraftDocument,
    "\n  mutation UpdateDraft($input: UpdateDraftBlacklistInput!) {\n    updateDraftBlacklist(input: $input) {\n      ... on UpdateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UpdateDraftDocument,
    "\n  query ValidateTenderOwner($input: ValidateTenderOwnerInput!) {\n    validateTenderOwner(input: $input) {\n      isOwner\n      reason\n    }\n  }\n": types.ValidateTenderOwnerDocument,
    "\n  query GetTenderDetail($input: TenderInput!) {\n    tenderDetail(input: $input) {\n      id\n      packageId\n      name\n      category\n      satker {\n        name\n      }\n      pagu\n      hps\n      budgetYear\n      kldi {\n        name\n      }\n      source\n    }\n  }\n": types.GetTenderDetailDocument,
    "\n  query AddFormRequiredData(\n    $providerId: String!\n    $source: ProviderSource = account\n  ) {\n    providerDetail(id: $providerId, source: $source) {\n      id\n      name\n      npwp\n      nib\n      additionalAddress\n      nibSource\n      address\n      regency {\n        name\n      }\n      province {\n        name\n      }\n      source\n    }\n    activeViolations {\n      id\n      name\n      description\n      month\n      year\n    }\n    lpseList {\n      id\n      name\n    }\n  }\n": types.AddFormRequiredDataDocument,
    "\n  mutation CreateAddendum($input: CreateBlacklistAddendumInput!) {\n    createBlacklistAddendum(input: $input) {\n      ... on CreateBlacklistAddendumResult {\n        __typename\n        blacklistID\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n": types.CreateAddendumDocument,
    "\n  query SearchAreaInput($input: SearchAreaInput!) {\n    searchArea(input: $input) {\n      ... on City {\n        id\n        name\n      }\n    }\n  }\n": types.SearchAreaInputDocument,
    "\n  mutation CreateDownloadSignedUrl($input: CreateDownloadSignedUrlInput!) {\n    createDownloadSignedUrl(input: $input) {\n      ... on CreateDownloadSignedUrlResult {\n        __typename\n        expiry\n        token\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.CreateDownloadSignedUrlDocument,
    "\n  mutation eSign($input: ESignInput!) {\n    eSign(input: $input) {\n      ... on ESignResult {\n        __typename\n        documentToken\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.ESignDocument,
    "\n  mutation UpdateBlacklistStatus($input: UpdateBlacklistStatusInput!) {\n    updateBlacklistStatus(input: $input) {\n      ... on UpdateBlacklistStatusResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n": types.UpdateBlacklistStatusDocument,
    "\n  query blackListDetail($id: ID!) {\n    completeBlacklistDetail(id: $id) {\n      ... on CompleteBlacklist {\n        id\n        skNumber\n        skNumberCanceled\n        skNumberCanceledTemporary\n        skNumberInitial\n        startDate\n        expiredDate\n        canceledDate\n        canceledTemporaryDate\n        status\n        provider {\n          id\n          source\n          providerId\n          name\n          npwp\n          nib\n          nibSource\n          address\n          additionalAddress\n          regency {\n            name\n          }\n          province {\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          year\n          month\n        }\n        tender {\n          id\n          name\n          packageId\n          budgetYear\n          hps\n          pagu\n          source\n          budgetYear\n          packageId\n          kldi {\n            id\n            name\n          }\n          satker {\n            id\n            name\n            address\n          }\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              size\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        history {\n          id\n          skNumber\n          status\n          description\n          createdAt\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n            name\n          }\n          kldi {\n            id\n            name\n          }\n        }\n        skAdditional {\n          id\n          officialPosition\n          instance {\n            logoFileToken\n            name\n            address\n            logoFileUrl\n          }\n          additionalDocumentData {\n            pokja {\n              letterNumber\n              letterDate\n            }\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n          }\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n  }\n": types.BlackListDetailDocument,
    "\n  query getBlacklistUser {\n    user {\n      ... on PaKpa {\n        id\n        name\n        email\n        phone\n        nip\n        kldi {\n          id\n          name\n        }\n        satker {\n          id\n          name\n          address\n        }\n      }\n    }\n  }\n": types.GetBlacklistUserDocument,
    "\n  query getUser {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        name\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n": types.GetUserDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteDraft($input: DeleteBlacklistInput!) {\n    deleteBlacklist(input: $input) {\n      ... on DeleteBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteDraft($input: DeleteBlacklistInput!) {\n    deleteBlacklist(input: $input) {\n      ... on DeleteBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetBlacklists($input: BlacklistsInput) {\n    blacklists(input: $input) {\n      blacklists {\n        id\n        skNumber\n        skNumberStatusBased\n        status\n        startDate\n        expiredDate\n        publishDate\n        publishDurationInMinutes\n        status\n        statusUpdatedAt\n        tender {\n          id\n          name\n          packageId\n          pagu\n          hps\n          budgetYear\n          category\n        }\n        provider {\n          id\n          name\n          npwp\n          address\n          additionalAddress\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n        }\n        correspondence {\n          lpse {\n            id\n            name\n          }\n          kldi {\n            name\n            id\n          }\n          satker {\n            id\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          month\n          year\n        }\n      }\n      pagination {\n        pageNumber\n        totalPage\n        totalData\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetBlacklists($input: BlacklistsInput) {\n    blacklists(input: $input) {\n      blacklists {\n        id\n        skNumber\n        skNumberStatusBased\n        status\n        startDate\n        expiredDate\n        publishDate\n        publishDurationInMinutes\n        status\n        statusUpdatedAt\n        tender {\n          id\n          name\n          packageId\n          pagu\n          hps\n          budgetYear\n          category\n        }\n        provider {\n          id\n          name\n          npwp\n          address\n          additionalAddress\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n        }\n        correspondence {\n          lpse {\n            id\n            name\n          }\n          kldi {\n            name\n            id\n          }\n          satker {\n            id\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          month\n          year\n        }\n      }\n      pagination {\n        pageNumber\n        totalPage\n        totalData\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SearchProvider($input: SearchProviderInput!) {\n    searchProvider(input: $input) {\n      id\n      source\n      name\n      npwp\n      nib\n      address\n    }\n  }\n"): (typeof documents)["\n  query SearchProvider($input: SearchProviderInput!) {\n    searchProvider(input: $input) {\n      id\n      source\n      name\n      npwp\n      nib\n      address\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query BlacklistsByProvider($id: String!, $source: ProviderSource = account) {\n    blacklistsByProvider(id: $id, source: $source) {\n      totalSanctionDurationInMinutes\n      blacklists {\n        id\n        skNumberStatusBased\n        publishDurationInMinutes\n        correspondence {\n          satker {\n            name\n            id\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query BlacklistsByProvider($id: String!, $source: ProviderSource = account) {\n    blacklistsByProvider(id: $id, source: $source) {\n      totalSanctionDurationInMinutes\n      blacklists {\n        id\n        skNumberStatusBased\n        publishDurationInMinutes\n        correspondence {\n          satker {\n            name\n            id\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query BlacklistHistory($id: ID!) {\n    blacklistDetail(id: $id) {\n      history {\n        status\n        skFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        supportFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        skNumber\n        id\n        description\n        createdAt\n      }\n    }\n  }\n"): (typeof documents)["\n  query BlacklistHistory($id: ID!) {\n    blacklistDetail(id: $id) {\n      history {\n        status\n        skFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        supportFile {\n          fileToken\n          id\n          fileUrl\n          name\n          size\n        }\n        skNumber\n        id\n        description\n        createdAt\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query BlacklistFormData(\n    $blacklistId: ID!\n    $syncSupplierData: Boolean\n    $syncUserData: Boolean\n  ) {\n    blacklistDetail(\n      id: $blacklistId\n      syncSupplierData: $syncSupplierData\n      syncUserData: $syncUserData\n    ) {\n      ... on Blacklist {\n        status\n        publishDate\n        #step 1\n        skNumber\n        document {\n          id\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        provider {\n          id\n          name\n          npwp\n          nib\n          additionalAddress\n          nibSource\n          address\n          regency {\n            name\n          }\n          province {\n            name\n          }\n          source\n        }\n        violation {\n          id\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n          }\n        }\n        tender {\n          id\n          category\n          packageId\n          name\n          satker {\n            name\n          }\n          pagu\n          hps\n          budgetYear\n          kldi {\n            name\n          }\n          source\n        }\n        startDate\n        expiredDate\n        #step 2\n        skAdditional {\n          instance {\n            logoFileToken\n            logoFileUrl\n            name\n            address\n          }\n          additionalDocumentData {\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n            pokja {\n              letterNumber\n              letterDate\n            }\n          }\n          officialPosition\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n    activeViolations {\n      ... on Violation {\n        id\n        name\n        description\n        month\n        year\n      }\n    }\n    lpseList {\n      ... on Lpse {\n        id\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query BlacklistFormData(\n    $blacklistId: ID!\n    $syncSupplierData: Boolean\n    $syncUserData: Boolean\n  ) {\n    blacklistDetail(\n      id: $blacklistId\n      syncSupplierData: $syncSupplierData\n      syncUserData: $syncUserData\n    ) {\n      ... on Blacklist {\n        status\n        publishDate\n        #step 1\n        skNumber\n        document {\n          id\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        provider {\n          id\n          name\n          npwp\n          nib\n          additionalAddress\n          nibSource\n          address\n          regency {\n            name\n          }\n          province {\n            name\n          }\n          source\n        }\n        violation {\n          id\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n          }\n        }\n        tender {\n          id\n          category\n          packageId\n          name\n          satker {\n            name\n          }\n          pagu\n          hps\n          budgetYear\n          kldi {\n            name\n          }\n          source\n        }\n        startDate\n        expiredDate\n        #step 2\n        skAdditional {\n          instance {\n            logoFileToken\n            logoFileUrl\n            name\n            address\n          }\n          additionalDocumentData {\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n            pokja {\n              letterNumber\n              letterDate\n            }\n          }\n          officialPosition\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n    activeViolations {\n      ... on Violation {\n        id\n        name\n        description\n        month\n        year\n      }\n    }\n    lpseList {\n      ... on Lpse {\n        id\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UploadFile($fileName: String!, $contentType: String!) {\n    createSignedUrl(\n      input: {\n        contentType: $contentType\n        identifier: $fileName\n        fileName: $fileName\n        isPublic: true\n      }\n    ) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UploadFile($fileName: String!, $contentType: String!) {\n    createSignedUrl(\n      input: {\n        contentType: $contentType\n        identifier: $fileName\n        fileName: $fileName\n        isPublic: true\n      }\n    ) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UploadFileFull($input: CreateSignedUrlInput!) {\n    createSignedUrl(input: $input) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UploadFileFull($input: CreateSignedUrlInput!) {\n    createSignedUrl(input: $input) {\n      ... on CreateSignedUrlResult {\n        __typename\n        token\n        signedUrl\n        identifier\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateBlacklist($input: CreateBlacklistInput!) {\n    createBlacklist(input: $input) {\n      ... on CreateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        code\n        message\n        reqId\n        __typename\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation CreateBlacklist($input: CreateBlacklistInput!) {\n    createBlacklist(input: $input) {\n      ... on CreateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        code\n        message\n        reqId\n        __typename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateBlacklist($input: UpdateBlacklistInput!) {\n    updateBlacklist(input: $input) {\n      ... on UpdateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateBlacklist($input: UpdateBlacklistInput!) {\n    updateBlacklist(input: $input) {\n      ... on UpdateBlacklistResult {\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateDraft($input: CreateDraftBlacklistInput!) {\n    createDraftBlacklist(input: $input) {\n      ... on CreateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation CreateDraft($input: CreateDraftBlacklistInput!) {\n    createDraftBlacklist(input: $input) {\n      ... on CreateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateDraft($input: UpdateDraftBlacklistInput!) {\n    updateDraftBlacklist(input: $input) {\n      ... on UpdateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateDraft($input: UpdateDraftBlacklistInput!) {\n    updateDraftBlacklist(input: $input) {\n      ... on UpdateDraftBlacklistResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ValidateTenderOwner($input: ValidateTenderOwnerInput!) {\n    validateTenderOwner(input: $input) {\n      isOwner\n      reason\n    }\n  }\n"): (typeof documents)["\n  query ValidateTenderOwner($input: ValidateTenderOwnerInput!) {\n    validateTenderOwner(input: $input) {\n      isOwner\n      reason\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetTenderDetail($input: TenderInput!) {\n    tenderDetail(input: $input) {\n      id\n      packageId\n      name\n      category\n      satker {\n        name\n      }\n      pagu\n      hps\n      budgetYear\n      kldi {\n        name\n      }\n      source\n    }\n  }\n"): (typeof documents)["\n  query GetTenderDetail($input: TenderInput!) {\n    tenderDetail(input: $input) {\n      id\n      packageId\n      name\n      category\n      satker {\n        name\n      }\n      pagu\n      hps\n      budgetYear\n      kldi {\n        name\n      }\n      source\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query AddFormRequiredData(\n    $providerId: String!\n    $source: ProviderSource = account\n  ) {\n    providerDetail(id: $providerId, source: $source) {\n      id\n      name\n      npwp\n      nib\n      additionalAddress\n      nibSource\n      address\n      regency {\n        name\n      }\n      province {\n        name\n      }\n      source\n    }\n    activeViolations {\n      id\n      name\n      description\n      month\n      year\n    }\n    lpseList {\n      id\n      name\n    }\n  }\n"): (typeof documents)["\n  query AddFormRequiredData(\n    $providerId: String!\n    $source: ProviderSource = account\n  ) {\n    providerDetail(id: $providerId, source: $source) {\n      id\n      name\n      npwp\n      nib\n      additionalAddress\n      nibSource\n      address\n      regency {\n        name\n      }\n      province {\n        name\n      }\n      source\n    }\n    activeViolations {\n      id\n      name\n      description\n      month\n      year\n    }\n    lpseList {\n      id\n      name\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateAddendum($input: CreateBlacklistAddendumInput!) {\n    createBlacklistAddendum(input: $input) {\n      ... on CreateBlacklistAddendumResult {\n        __typename\n        blacklistID\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation CreateAddendum($input: CreateBlacklistAddendumInput!) {\n    createBlacklistAddendum(input: $input) {\n      ... on CreateBlacklistAddendumResult {\n        __typename\n        blacklistID\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n        code\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SearchAreaInput($input: SearchAreaInput!) {\n    searchArea(input: $input) {\n      ... on City {\n        id\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query SearchAreaInput($input: SearchAreaInput!) {\n    searchArea(input: $input) {\n      ... on City {\n        id\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateDownloadSignedUrl($input: CreateDownloadSignedUrlInput!) {\n    createDownloadSignedUrl(input: $input) {\n      ... on CreateDownloadSignedUrlResult {\n        __typename\n        expiry\n        token\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation CreateDownloadSignedUrl($input: CreateDownloadSignedUrlInput!) {\n    createDownloadSignedUrl(input: $input) {\n      ... on CreateDownloadSignedUrlResult {\n        __typename\n        expiry\n        token\n        url\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation eSign($input: ESignInput!) {\n    eSign(input: $input) {\n      ... on ESignResult {\n        __typename\n        documentToken\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation eSign($input: ESignInput!) {\n    eSign(input: $input) {\n      ... on ESignResult {\n        __typename\n        documentToken\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateBlacklistStatus($input: UpdateBlacklistStatusInput!) {\n    updateBlacklistStatus(input: $input) {\n      ... on UpdateBlacklistStatusResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateBlacklistStatus($input: UpdateBlacklistStatusInput!) {\n    updateBlacklistStatus(input: $input) {\n      ... on UpdateBlacklistStatusResult {\n        __typename\n        id\n      }\n      ... on GenericError {\n        __typename\n        code\n        message\n        reqId\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query blackListDetail($id: ID!) {\n    completeBlacklistDetail(id: $id) {\n      ... on CompleteBlacklist {\n        id\n        skNumber\n        skNumberCanceled\n        skNumberCanceledTemporary\n        skNumberInitial\n        startDate\n        expiredDate\n        canceledDate\n        canceledTemporaryDate\n        status\n        provider {\n          id\n          source\n          providerId\n          name\n          npwp\n          nib\n          nibSource\n          address\n          additionalAddress\n          regency {\n            name\n          }\n          province {\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          year\n          month\n        }\n        tender {\n          id\n          name\n          packageId\n          budgetYear\n          hps\n          pagu\n          source\n          budgetYear\n          packageId\n          kldi {\n            id\n            name\n          }\n          satker {\n            id\n            name\n            address\n          }\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              size\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        history {\n          id\n          skNumber\n          status\n          description\n          createdAt\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n            name\n          }\n          kldi {\n            id\n            name\n          }\n        }\n        skAdditional {\n          id\n          officialPosition\n          instance {\n            logoFileToken\n            name\n            address\n            logoFileUrl\n          }\n          additionalDocumentData {\n            pokja {\n              letterNumber\n              letterDate\n            }\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n          }\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query blackListDetail($id: ID!) {\n    completeBlacklistDetail(id: $id) {\n      ... on CompleteBlacklist {\n        id\n        skNumber\n        skNumberCanceled\n        skNumberCanceledTemporary\n        skNumberInitial\n        startDate\n        expiredDate\n        canceledDate\n        canceledTemporaryDate\n        status\n        provider {\n          id\n          source\n          providerId\n          name\n          npwp\n          nib\n          nibSource\n          address\n          additionalAddress\n          regency {\n            name\n          }\n          province {\n            name\n          }\n        }\n        violation {\n          id\n          name\n          description\n          year\n          month\n        }\n        tender {\n          id\n          name\n          packageId\n          budgetYear\n          hps\n          pagu\n          source\n          budgetYear\n          packageId\n          kldi {\n            id\n            name\n          }\n          satker {\n            id\n            name\n            address\n          }\n        }\n        document {\n          id\n          name\n          blacklistId\n          additionalInfo\n          uploads {\n            id\n            category\n            file {\n              id\n              name\n              size\n              fileToken\n              fileUrl\n            }\n          }\n        }\n        history {\n          id\n          skNumber\n          status\n          description\n          createdAt\n        }\n        correspondence {\n          name\n          email\n          phone\n          lpse {\n            id\n            name\n          }\n          kldi {\n            id\n            name\n          }\n        }\n        skAdditional {\n          id\n          officialPosition\n          instance {\n            logoFileToken\n            name\n            address\n            logoFileUrl\n          }\n          additionalDocumentData {\n            pokja {\n              letterNumber\n              letterDate\n            }\n            apip {\n              letterNumber\n              letterDate\n              summary\n            }\n            requestApip {\n              letterNumber\n              letterDate\n              summary\n            }\n            court {\n              courtName\n              letterNumber\n              letterDate\n              description\n            }\n          }\n          violationDetail {\n            realizationPercentage\n            providerAction\n          }\n          cc\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getBlacklistUser {\n    user {\n      ... on PaKpa {\n        id\n        name\n        email\n        phone\n        nip\n        kldi {\n          id\n          name\n        }\n        satker {\n          id\n          name\n          address\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query getBlacklistUser {\n    user {\n      ... on PaKpa {\n        id\n        name\n        email\n        phone\n        nip\n        kldi {\n          id\n          name\n        }\n        satker {\n          id\n          name\n          address\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getUser {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        name\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUser {\n    me {\n      ... on UserInfo {\n        __typename\n        id\n        email\n        name\n        username\n        type\n        phone\n      }\n      ... on GenericError {\n        __typename\n        reqId\n        message\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;