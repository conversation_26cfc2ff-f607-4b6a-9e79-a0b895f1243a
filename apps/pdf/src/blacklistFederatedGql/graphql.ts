/* eslint-disable */
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Time: { input: any; output: any; }
  Upload: { input: any; output: any; }
};

export type Area = City;

export enum AreaType {
  City = 'city',
  Province = 'province'
}

export type AuthProvider = {
  __typename?: 'AuthProvider';
  auth0Sub?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['String']['output']>;
};

export type Blacklist = {
  __typename?: 'Blacklist';
  canceledDate?: Maybe<Scalars['Time']['output']>;
  canceledTemporaryDate?: Maybe<Scalars['Time']['output']>;
  correspondence: BlacklistCorrespondence;
  document?: Maybe<BlacklistDocument>;
  expiredDate?: Maybe<Scalars['Time']['output']>;
  history: Array<BlacklistHistory>;
  id: Scalars['ID']['output'];
  provider: Provider;
  publishDate?: Maybe<Scalars['Time']['output']>;
  publishDurationInMinutes?: Maybe<Scalars['Int']['output']>;
  skAdditional?: Maybe<BlacklistSkAdditional>;
  skNumber?: Maybe<Scalars['String']['output']>;
  skNumberCanceled?: Maybe<Scalars['String']['output']>;
  skNumberCanceledTemporary?: Maybe<Scalars['String']['output']>;
  skNumberInitial?: Maybe<Scalars['String']['output']>;
  skNumberStatusBased?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['Time']['output']>;
  status: BlacklistStatus;
  statusUpdatedAt?: Maybe<Scalars['Time']['output']>;
  tender?: Maybe<Tender>;
  violation?: Maybe<Violation>;
};

export type BlacklistAddendumInstanceInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  logoFileToken?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistAddendumSkAdditionalInput = {
  additionalDocumentData?: InputMaybe<BlacklistAdditionalDocumentInput>;
  cc?: InputMaybe<Array<Scalars['String']['input']>>;
  instance?: InputMaybe<BlacklistAddendumInstanceInput>;
  officialPosition?: InputMaybe<Scalars['String']['input']>;
  violationDetail?: InputMaybe<BlacklistAddendumViolationDetailInput>;
};

export type BlacklistAddendumViolationDetailInput = {
  providerAction?: InputMaybe<Scalars['String']['input']>;
  realizationPercentage?: InputMaybe<Scalars['Float']['input']>;
};

export type BlacklistAdditionalDocument = {
  __typename?: 'BlacklistAdditionalDocument';
  apip?: Maybe<BlacklistApip>;
  court?: Maybe<BlacklistCourt>;
  pokja?: Maybe<BlacklistPokja>;
  requestApip?: Maybe<BlacklistRequestApip>;
  verdict?: Maybe<BlacklistVerdict>;
};

export type BlacklistAdditionalDocumentInput = {
  apip?: InputMaybe<BlacklistApipInput>;
  court?: InputMaybe<BlacklistCourtInput>;
  pokja?: InputMaybe<BlacklistPokjaInput>;
  requestApip?: InputMaybe<BlacklistRequestApipInput>;
  verdict?: InputMaybe<BlacklistVerdictInput>;
};

export type BlacklistApip = {
  __typename?: 'BlacklistApip';
  letterDate?: Maybe<Scalars['Time']['output']>;
  letterNumber?: Maybe<Scalars['String']['output']>;
  summary?: Maybe<Scalars['String']['output']>;
};

export type BlacklistApipInput = {
  letterDate: Scalars['Time']['input'];
  letterNumber: Scalars['String']['input'];
  summary: Scalars['String']['input'];
};

export type BlacklistCorrespondence = {
  __typename?: 'BlacklistCorrespondence';
  email: Scalars['String']['output'];
  kldi?: Maybe<Kldi>;
  lpse: Lpse;
  name: Scalars['String']['output'];
  phone: Scalars['String']['output'];
  satker?: Maybe<Satker>;
};

export type BlacklistCorrespondenceInput = {
  lpseId: Scalars['ID']['input'];
};

export type BlacklistCourt = {
  __typename?: 'BlacklistCourt';
  courtName?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  letterDate?: Maybe<Scalars['Time']['output']>;
  letterNumber?: Maybe<Scalars['String']['output']>;
};

export type BlacklistCourtInput = {
  courtName: Scalars['String']['input'];
  description: Scalars['String']['input'];
  letterDate: Scalars['Time']['input'];
  letterNumber: Scalars['String']['input'];
};

export type BlacklistDocument = {
  __typename?: 'BlacklistDocument';
  additionalInfo?: Maybe<Scalars['String']['output']>;
  blacklistId?: Maybe<Scalars['ID']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  uploads: Array<BlacklistUpload>;
};

export type BlacklistDocumentInput = {
  additionalInfo?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  uploads: Array<BlacklistUploadInput>;
};

export type BlacklistDraftAdditionalDocumentInput = {
  apip?: InputMaybe<BlacklistDraftApipInput>;
  court?: InputMaybe<BlacklistDraftCourtInput>;
  pokja?: InputMaybe<BlacklistDraftPokjaInput>;
  requestApip?: InputMaybe<BlacklistDraftRequestApipInput>;
  verdict?: InputMaybe<BlacklistDraftVerdictInput>;
};

export type BlacklistDraftApipInput = {
  letterDate?: InputMaybe<Scalars['Time']['input']>;
  letterNumber?: InputMaybe<Scalars['String']['input']>;
  summary?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistDraftCorrespondenceInput = {
  lpseId?: InputMaybe<Scalars['ID']['input']>;
};

export type BlacklistDraftCourtInput = {
  courtName?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  letterDate?: InputMaybe<Scalars['Time']['input']>;
  letterNumber?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistDraftDocumentInput = {
  additionalInfo?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  uploads?: InputMaybe<Array<BlacklistUploadInput>>;
};

export type BlacklistDraftInstanceInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  logoFileToken?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistDraftPokjaInput = {
  letterDate?: InputMaybe<Scalars['Time']['input']>;
  letterNumber?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistDraftRequestApipInput = {
  letterDate?: InputMaybe<Scalars['Time']['input']>;
  letterNumber?: InputMaybe<Scalars['String']['input']>;
  summary?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistDraftSkAdditionalInput = {
  additionalDocumentData?: InputMaybe<BlacklistDraftAdditionalDocumentInput>;
  cc?: InputMaybe<Array<Scalars['String']['input']>>;
  instance?: InputMaybe<BlacklistDraftInstanceInput>;
  officialPosition?: InputMaybe<Scalars['String']['input']>;
  violationDetail?: InputMaybe<BlacklistDraftViolationDetailInput>;
};

export type BlacklistDraftVerdictInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  letterDate?: InputMaybe<Scalars['Time']['input']>;
};

export type BlacklistDraftViolationDetailInput = {
  providerAction?: InputMaybe<Scalars['String']['input']>;
  realizationPercentage?: InputMaybe<Scalars['Float']['input']>;
};

export type BlacklistDraftViolationInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  sk?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistFilterInput = {
  endDate?: InputMaybe<Scalars['Time']['input']>;
  query?: InputMaybe<Scalars['String']['input']>;
  skNumber?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
  status?: InputMaybe<BlacklistStatus>;
};

export type BlacklistHistory = {
  __typename?: 'BlacklistHistory';
  createdAt: Scalars['Time']['output'];
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  skFile?: Maybe<File>;
  skNumber?: Maybe<Scalars['String']['output']>;
  status: BlacklistStatus;
  supportFile?: Maybe<File>;
};

export type BlacklistInstance = {
  __typename?: 'BlacklistInstance';
  address?: Maybe<Scalars['String']['output']>;
  logoFileToken?: Maybe<Scalars['String']['output']>;
  logoFileUrl?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type BlacklistInstanceInput = {
  address: Scalars['String']['input'];
  logoFileToken: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type BlacklistPacketInput = {
  id: Scalars['ID']['input'];
  source: TenderSource;
};

export type BlacklistPokja = {
  __typename?: 'BlacklistPokja';
  letterDate?: Maybe<Scalars['Time']['output']>;
  letterNumber?: Maybe<Scalars['String']['output']>;
};

export type BlacklistPokjaInput = {
  letterDate: Scalars['Time']['input'];
  letterNumber: Scalars['String']['input'];
};

export type BlacklistProviderInput = {
  additionalInfo?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  nib?: InputMaybe<Scalars['String']['input']>;
  source?: InputMaybe<ProviderSource>;
};

export type BlacklistRequestApip = {
  __typename?: 'BlacklistRequestApip';
  letterDate?: Maybe<Scalars['Time']['output']>;
  letterNumber?: Maybe<Scalars['String']['output']>;
  summary?: Maybe<Scalars['String']['output']>;
};

export type BlacklistRequestApipInput = {
  letterDate: Scalars['Time']['input'];
  letterNumber: Scalars['String']['input'];
  summary: Scalars['String']['input'];
};

export type BlacklistSkAdditional = {
  __typename?: 'BlacklistSkAdditional';
  additionalDocumentData?: Maybe<BlacklistAdditionalDocument>;
  cc?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['ID']['output'];
  instance?: Maybe<BlacklistInstance>;
  officialPosition?: Maybe<Scalars['String']['output']>;
  violationDetail?: Maybe<BlacklistViolationDetail>;
};

export type BlacklistSkAdditionalInput = {
  additionalDocumentData: BlacklistAdditionalDocumentInput;
  cc?: InputMaybe<Array<Scalars['String']['input']>>;
  instance: BlacklistInstanceInput;
  officialPosition: Scalars['String']['input'];
  violationDetail: BlacklistViolationDetailInput;
};

export type BlacklistSort = {
  direction: SortDirection;
  field: SortField;
};

export enum BlacklistStatus {
  Canceled = 'CANCELED',
  CanceledTemporary = 'CANCELED_TEMPORARY',
  Draft = 'DRAFT',
  DraftComplete = 'DRAFT_COMPLETE',
  Expired = 'EXPIRED',
  Published = 'PUBLISHED'
}

export type BlacklistUpdateCourtInput = {
  courtCity: Scalars['String']['input'];
  courtSentence: Scalars['String']['input'];
  description: Scalars['String']['input'];
  letterDate: Scalars['Time']['input'];
  letterNumber: Scalars['String']['input'];
};

export type BlacklistUpdateProviderInput = {
  additionalInfo?: InputMaybe<Scalars['String']['input']>;
  nib?: InputMaybe<Scalars['String']['input']>;
};

export type BlacklistUpload = {
  __typename?: 'BlacklistUpload';
  category: FileCategory;
  file: File;
  id: Scalars['ID']['output'];
};

export type BlacklistUploadInput = {
  category: FileCategory;
  file: FileInput;
};

export type BlacklistVerdict = {
  __typename?: 'BlacklistVerdict';
  description?: Maybe<Scalars['String']['output']>;
  letterDate?: Maybe<Scalars['Time']['output']>;
};

export type BlacklistVerdictInput = {
  description: Scalars['String']['input'];
  letterDate: Scalars['Time']['input'];
};

export type BlacklistViolationDetail = {
  __typename?: 'BlacklistViolationDetail';
  providerAction?: Maybe<Scalars['String']['output']>;
  realizationPercentage?: Maybe<Scalars['Float']['output']>;
};

export type BlacklistViolationDetailInput = {
  providerAction: Scalars['String']['input'];
  realizationPercentage: Scalars['Float']['input'];
};

export type BlacklistViolationInput = {
  id: Scalars['ID']['input'];
  sk: Scalars['String']['input'];
};

export type BlacklistsByProviderOutput = {
  __typename?: 'BlacklistsByProviderOutput';
  blacklists: Array<Blacklist>;
  totalSanctionDurationFormatted: Scalars['String']['output'];
  totalSanctionDurationInMinutes: Scalars['Int']['output'];
};

export type BlacklistsInput = {
  filter?: InputMaybe<BlacklistFilterInput>;
  pagination?: InputMaybe<PaginationInput>;
  sort?: InputMaybe<BlacklistSort>;
};

export type BlacklistsOutput = {
  __typename?: 'BlacklistsOutput';
  blacklists: Array<Blacklist>;
  pagination: Pagination;
};

export type City = {
  __typename?: 'City';
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type CompleteBlacklist = {
  __typename?: 'CompleteBlacklist';
  canceledDate?: Maybe<Scalars['Time']['output']>;
  canceledTemporaryDate?: Maybe<Scalars['Time']['output']>;
  correspondence: BlacklistCorrespondence;
  document: BlacklistDocument;
  expiredDate: Scalars['Time']['output'];
  history: Array<BlacklistHistory>;
  id: Scalars['ID']['output'];
  provider: Provider;
  publishDate?: Maybe<Scalars['Time']['output']>;
  publishDurationInMinutes?: Maybe<Scalars['Int']['output']>;
  skAdditional: CompleteBlacklistSkAdditional;
  skNumber: Scalars['String']['output'];
  skNumberCanceled?: Maybe<Scalars['String']['output']>;
  skNumberCanceledTemporary?: Maybe<Scalars['String']['output']>;
  skNumberInitial?: Maybe<Scalars['String']['output']>;
  startDate: Scalars['Time']['output'];
  status: BlacklistStatus;
  tender?: Maybe<Tender>;
  violation: Violation;
};

export type CompleteBlacklistAdditionalDocument = {
  __typename?: 'CompleteBlacklistAdditionalDocument';
  apip?: Maybe<CompleteBlacklistApip>;
  court?: Maybe<CompleteBlacklistCourt>;
  courtWithdraw?: Maybe<CompleteBlacklistWithdrawCourt>;
  courtWithdrawTemp?: Maybe<CompleteBlacklistWithdrawTempCourt>;
  pokja?: Maybe<CompleteBlacklistPokja>;
  requestApip?: Maybe<CompleteBlacklistRequestApip>;
  verdict?: Maybe<CompleteBlacklistVerdict>;
};

export type CompleteBlacklistApip = {
  __typename?: 'CompleteBlacklistApip';
  letterDate: Scalars['Time']['output'];
  letterNumber: Scalars['String']['output'];
  summary: Scalars['String']['output'];
};

export type CompleteBlacklistCourt = {
  __typename?: 'CompleteBlacklistCourt';
  courtName: Scalars['String']['output'];
  description: Scalars['String']['output'];
  letterDate: Scalars['Time']['output'];
  letterNumber: Scalars['String']['output'];
};

export type CompleteBlacklistInstance = {
  __typename?: 'CompleteBlacklistInstance';
  address: Scalars['String']['output'];
  logoFileToken: Scalars['String']['output'];
  logoFileUrl: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type CompleteBlacklistPokja = {
  __typename?: 'CompleteBlacklistPokja';
  letterDate: Scalars['Time']['output'];
  letterNumber: Scalars['String']['output'];
};

export type CompleteBlacklistRequestApip = {
  __typename?: 'CompleteBlacklistRequestApip';
  letterDate: Scalars['Time']['output'];
  letterNumber: Scalars['String']['output'];
  summary: Scalars['String']['output'];
};

export type CompleteBlacklistSkAdditional = {
  __typename?: 'CompleteBlacklistSkAdditional';
  additionalDocumentData: CompleteBlacklistAdditionalDocument;
  cc?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['ID']['output'];
  instance: CompleteBlacklistInstance;
  officialPosition: Scalars['String']['output'];
  violationDetail: CompleteBlacklistViolationDetail;
};

export type CompleteBlacklistVerdict = {
  __typename?: 'CompleteBlacklistVerdict';
  description: Scalars['String']['output'];
  letterDate: Scalars['Time']['output'];
};

export type CompleteBlacklistViolationDetail = {
  __typename?: 'CompleteBlacklistViolationDetail';
  providerAction: Scalars['String']['output'];
  realizationPercentage: Scalars['Float']['output'];
};

export type CompleteBlacklistWithdrawCourt = {
  __typename?: 'CompleteBlacklistWithdrawCourt';
  courtCity: Scalars['String']['output'];
  courtSentence: Scalars['String']['output'];
  letterDate: Scalars['Time']['output'];
  letterNumber: Scalars['String']['output'];
};

export type CompleteBlacklistWithdrawTempCourt = {
  __typename?: 'CompleteBlacklistWithdrawTempCourt';
  courtCity: Scalars['String']['output'];
  courtSentence: Scalars['String']['output'];
  letterDate: Scalars['Time']['output'];
  letterNumber: Scalars['String']['output'];
};

export type CreateBlacklistAddendumInput = {
  blacklistId: Scalars['ID']['input'];
  skAdditional: BlacklistAddendumSkAdditionalInput;
};

export type CreateBlacklistAddendumResponse = CreateBlacklistAddendumResult | GenericError;

export type CreateBlacklistAddendumResult = {
  __typename?: 'CreateBlacklistAddendumResult';
  blacklistID: Scalars['ID']['output'];
};

export type CreateBlacklistInput = {
  correspondence: BlacklistCorrespondenceInput;
  document: BlacklistDocumentInput;
  packet: BlacklistPacketInput;
  provider: BlacklistProviderInput;
  skAdditionalData: BlacklistSkAdditionalInput;
  violation: BlacklistViolationInput;
};

export type CreateBlacklistResponse = CreateBlacklistResult | GenericError;

export type CreateBlacklistResult = {
  __typename?: 'CreateBlacklistResult';
  id: Scalars['ID']['output'];
};

export type CreateDownloadSignedUrlInput = {
  token: Scalars['String']['input'];
};

export type CreateDownloadSignedUrlResponse = CreateDownloadSignedUrlResult | GenericError;

export type CreateDownloadSignedUrlResult = {
  __typename?: 'CreateDownloadSignedUrlResult';
  expiry: Scalars['Int']['output'];
  token: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type CreateDraftBlacklistInput = {
  correspondence?: InputMaybe<BlacklistDraftCorrespondenceInput>;
  document?: InputMaybe<BlacklistDraftDocumentInput>;
  packet?: InputMaybe<BlacklistPacketInput>;
  provider: BlacklistProviderInput;
  skAdditionalData?: InputMaybe<BlacklistDraftSkAdditionalInput>;
  violation?: InputMaybe<BlacklistDraftViolationInput>;
};

export type CreateDraftBlacklistResponse = CreateDraftBlacklistResult | GenericError;

export type CreateDraftBlacklistResult = {
  __typename?: 'CreateDraftBlacklistResult';
  id: Scalars['ID']['output'];
};

export type CreateSignedUrlInput = {
  contentType: Scalars['String']['input'];
  fileName: Scalars['String']['input'];
  identifier: Scalars['String']['input'];
  isPublic: Scalars['Boolean']['input'];
};

export type CreateSignedUrlResponse = CreateSignedUrlResult | GenericError;

export type CreateSignedUrlResult = {
  __typename?: 'CreateSignedUrlResult';
  expiry: Scalars['Int']['output'];
  identifier: Scalars['String']['output'];
  signedUrl: Scalars['String']['output'];
  token: Scalars['String']['output'];
};

export type CreateUploadSignedUrlInput = {
  contentType: Scalars['String']['input'];
  fileName: Scalars['String']['input'];
  identifier: Scalars['String']['input'];
  isPublic: Scalars['Boolean']['input'];
};

export type CreateUploadSignedUrlResponse = CreateUploadSignedUrlResult | GenericError;

export type CreateUploadSignedUrlResult = {
  __typename?: 'CreateUploadSignedUrlResult';
  expiry: Scalars['Int']['output'];
  identifier: Scalars['String']['output'];
  signedUrl: Scalars['String']['output'];
  token: Scalars['String']['output'];
};

export type DeleteBlacklistInput = {
  blacklistId: Scalars['ID']['input'];
};

export type DeleteBlacklistResponse = DeleteBlacklistResult | GenericError;

export type DeleteBlacklistResult = {
  __typename?: 'DeleteBlacklistResult';
  id: Scalars['ID']['output'];
};

export type ESignInput = {
  blacklistId: Scalars['ID']['input'];
  documentToken: Scalars['String']['input'];
  height: Scalars['Float']['input'];
  passPhrase: Scalars['String']['input'];
  posX: Scalars['Float']['input'];
  posY: Scalars['Float']['input'];
  signLocation?: InputMaybe<Scalars['String']['input']>;
  signPage: Scalars['Int']['input'];
  status?: InputMaybe<BlacklistStatus>;
  width: Scalars['Float']['input'];
};

export type ESignResponse = ESignResult | GenericError;

export type ESignResult = {
  __typename?: 'ESignResult';
  documentToken: Scalars['String']['output'];
};

export type File = {
  __typename?: 'File';
  fileToken?: Maybe<Scalars['String']['output']>;
  fileUrl?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  size: Scalars['Int']['output'];
};

export enum FileCategory {
  SkCourt = 'SK_COURT',
  SkDecisionPaKpa = 'SK_DECISION_PA_KPA',
  SkDecisionPaKpaGenerated = 'SK_DECISION_PA_KPA_GENERATED',
  SkDeliveryPaKpa = 'SK_DELIVERY_PA_KPA',
  SkFindingBpkApip = 'SK_FINDING_BPK_APIP',
  SkObjectionProvider = 'SK_OBJECTION_PROVIDER',
  SkProposalPpkPokjaUlpPp = 'SK_PROPOSAL_PPK_POKJA_ULP_PP',
  SkRecommendationApip = 'SK_RECOMMENDATION_APIP',
  SkRequestRecommendationApip = 'SK_REQUEST_RECOMMENDATION_APIP',
  Support = 'SUPPORT',
  Blacklist = 'blacklist',
  Pencabutan = 'pencabutan',
  PencabutanSementara = 'pencabutan_sementara'
}

export type FileInput = {
  fileToken: Scalars['String']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  name: Scalars['String']['input'];
};

export type GenericError = {
  __typename?: 'GenericError';
  code: Scalars['String']['output'];
  message: Scalars['String']['output'];
  reqId: Scalars['String']['output'];
};

export type GetUploadStatusInput = {
  token: Scalars['ID']['input'];
};

export type Kldi = {
  __typename?: 'Kldi';
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type Lpse = {
  __typename?: 'Lpse';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type MeResponse = GenericError | UserInfo;

export type Mutation = {
  __typename?: 'Mutation';
  createBlacklist: CreateBlacklistResponse;
  createBlacklistAddendum: CreateBlacklistAddendumResponse;
  createDownloadSignedUrl: CreateDownloadSignedUrlResponse;
  createDraftBlacklist: CreateDraftBlacklistResponse;
  createSignedUrl: CreateSignedUrlResponse;
  createUploadSignedUrl: CreateUploadSignedUrlResponse;
  deleteBlacklist: DeleteBlacklistResponse;
  eSign: ESignResponse;
  updateBlacklist: UpdateBlacklistResponse;
  updateBlacklistStatus: UpdateBlacklistStatusResponse;
  updateDraftBlacklist: UpdateDraftBlacklistResponse;
};


export type MutationCreateBlacklistArgs = {
  input: CreateBlacklistInput;
};


export type MutationCreateBlacklistAddendumArgs = {
  input: CreateBlacklistAddendumInput;
};


export type MutationCreateDownloadSignedUrlArgs = {
  input: CreateDownloadSignedUrlInput;
};


export type MutationCreateDraftBlacklistArgs = {
  input: CreateDraftBlacklistInput;
};


export type MutationCreateSignedUrlArgs = {
  input: CreateSignedUrlInput;
};


export type MutationCreateUploadSignedUrlArgs = {
  input: CreateUploadSignedUrlInput;
};


export type MutationDeleteBlacklistArgs = {
  input: DeleteBlacklistInput;
};


export type MutationESignArgs = {
  input: ESignInput;
};


export type MutationUpdateBlacklistArgs = {
  input: UpdateBlacklistInput;
};


export type MutationUpdateBlacklistStatusArgs = {
  input: UpdateBlacklistStatusInput;
};


export type MutationUpdateDraftBlacklistArgs = {
  input: UpdateDraftBlacklistInput;
};

export enum NibSource {
  Account = 'account',
  Sikap = 'sikap',
  Unknown = 'unknown',
  User = 'user'
}

export type PaKpa = {
  __typename?: 'PaKpa';
  email: Scalars['String']['output'];
  id: Scalars['String']['output'];
  kldi: Kldi;
  name: Scalars['String']['output'];
  nip: Scalars['String']['output'];
  phone: Scalars['String']['output'];
  satker: Satker;
};

export type Pagination = {
  __typename?: 'Pagination';
  pageNumber: Scalars['Int']['output'];
  perPage: Scalars['Int']['output'];
  totalData?: Maybe<Scalars['Int']['output']>;
  totalPage: Scalars['Int']['output'];
};

export type PaginationInput = {
  pageNumber: Scalars['Int']['input'];
  perPage: Scalars['Int']['input'];
};

export enum Profession {
  Asn = 'ASN',
  NonAsn = 'NON_ASN'
}

export type ProfileInfo = {
  __typename?: 'ProfileInfo';
  dob?: Maybe<Scalars['String']['output']>;
  nik?: Maybe<Scalars['String']['output']>;
  nip?: Maybe<Scalars['String']['output']>;
};

export enum ProfileStatus {
  NikDuplicatedPending = 'NIK_DUPLICATED_PENDING',
  NipDuplicatedPending = 'NIP_DUPLICATED_PENDING',
  NipNikDuplicatedPending = 'NIP_NIK_DUPLICATED_PENDING',
  ProfileDocsUploaded = 'PROFILE_DOCS_UPLOADED',
  ProfileReject = 'PROFILE_REJECT',
  ProfileVerified = 'PROFILE_VERIFIED',
  ScheduleAssigned = 'SCHEDULE_ASSIGNED'
}

export type Provider = {
  __typename?: 'Provider';
  additionalAddress?: Maybe<Scalars['String']['output']>;
  address: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  nib: Scalars['String']['output'];
  nibSource: NibSource;
  npwp: Scalars['String']['output'];
  providerId: Scalars['Int']['output'];
  province: Province;
  regency: Regency;
  source: ProviderSource;
};

export enum ProviderSource {
  Account = 'account',
  Sikap = 'sikap'
}

export type Province = {
  __typename?: 'Province';
  name: Scalars['String']['output'];
};

export type Query = {
  __typename?: 'Query';
  activeViolations: Array<Violation>;
  blacklistDetail?: Maybe<Blacklist>;
  blacklists: BlacklistsOutput;
  blacklistsByProvider: BlacklistsByProviderOutput;
  completeBlacklistDetail?: Maybe<CompleteBlacklist>;
  dummyRequest?: Maybe<Scalars['String']['output']>;
  lpseList: Array<Lpse>;
  me?: Maybe<MeResponse>;
  providerDetail?: Maybe<Provider>;
  searchArea: Array<Area>;
  searchProvider: Array<Provider>;
  tenderDetail?: Maybe<Tender>;
  user: User;
  validateSkNumber: ValidateSkNumberOutput;
  validateTenderOwner: ValidateTenderOwnerOutput;
};


export type QueryBlacklistDetailArgs = {
  id: Scalars['ID']['input'];
  syncSupplierData?: InputMaybe<Scalars['Boolean']['input']>;
  syncUserData?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryBlacklistsArgs = {
  input?: InputMaybe<BlacklistsInput>;
};


export type QueryBlacklistsByProviderArgs = {
  id: Scalars['String']['input'];
  source?: InputMaybe<ProviderSource>;
};


export type QueryCompleteBlacklistDetailArgs = {
  id: Scalars['ID']['input'];
};


export type QueryProviderDetailArgs = {
  id: Scalars['String']['input'];
  source?: InputMaybe<ProviderSource>;
};


export type QuerySearchAreaArgs = {
  input: SearchAreaInput;
};


export type QuerySearchProviderArgs = {
  input: SearchProviderInput;
};


export type QueryTenderDetailArgs = {
  input: TenderInput;
};


export type QueryValidateSkNumberArgs = {
  input: ValidateSkNumberInput;
};


export type QueryValidateTenderOwnerArgs = {
  input: ValidateTenderOwnerInput;
};

export type Regency = {
  __typename?: 'Regency';
  name: Scalars['String']['output'];
};

export type Satker = {
  __typename?: 'Satker';
  address: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type SearchAreaInput = {
  keyword: Scalars['String']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  type: AreaType;
};

export type SearchProviderInput = {
  keyword: Scalars['String']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  type: SearchProviderType;
};

export enum SearchProviderType {
  Name = 'name',
  Npwp = 'npwp'
}

export enum SortDirection {
  Ascending = 'ASCENDING',
  Descending = 'DESCENDING'
}

export enum SortField {
  CreatedAt = 'CREATED_AT',
  DocumentName = 'DOCUMENT_NAME',
  PackageId = 'PACKAGE_ID',
  ProviderName = 'PROVIDER_NAME',
  PublishDurationInMinutes = 'PUBLISH_DURATION_IN_MINUTES',
  SkNumber = 'SK_NUMBER',
  StartDate = 'START_DATE',
  Status = 'STATUS',
  StatusUpdatedAt = 'STATUS_UPDATED_AT',
  TenderName = 'TENDER_NAME'
}

export type Subscription = {
  __typename?: 'Subscription';
  getUploadStatus: Scalars['String']['output'];
};


export type SubscriptionGetUploadStatusArgs = {
  input?: InputMaybe<GetUploadStatusInput>;
};

export type Tender = {
  __typename?: 'Tender';
  budgetYear: Scalars['Int']['output'];
  category: Scalars['String']['output'];
  hps: Scalars['Int']['output'];
  id: Scalars['ID']['output'];
  kldi: Kldi;
  name: Scalars['String']['output'];
  packageId: Scalars['ID']['output'];
  pagu: Scalars['Int']['output'];
  satker: Satker;
  source: TenderSource;
};

export type TenderInput = {
  id: Scalars['ID']['input'];
  source: TenderSource;
};

export enum TenderSource {
  Procurement = 'procurement',
  Rup = 'rup'
}

export type UpdateBlacklistInput = {
  blacklistId?: InputMaybe<Scalars['ID']['input']>;
  correspondence: BlacklistCorrespondenceInput;
  document: BlacklistDocumentInput;
  packet: BlacklistPacketInput;
  provider: BlacklistUpdateProviderInput;
  skAdditionalData: BlacklistSkAdditionalInput;
  violation: BlacklistViolationInput;
};

export type UpdateBlacklistResponse = GenericError | UpdateBlacklistResult;

export type UpdateBlacklistResult = {
  __typename?: 'UpdateBlacklistResult';
  id: Scalars['ID']['output'];
};

export type UpdateBlacklistStatusInput = {
  blacklistId: Scalars['ID']['input'];
  court?: InputMaybe<BlacklistUpdateCourtInput>;
  sendNotification?: InputMaybe<Scalars['Boolean']['input']>;
  skFile: FileInput;
  skNumber: Scalars['String']['input'];
  status: BlacklistStatus;
  supportFile?: InputMaybe<FileInput>;
};

export type UpdateBlacklistStatusResponse = GenericError | UpdateBlacklistStatusResult;

export type UpdateBlacklistStatusResult = {
  __typename?: 'UpdateBlacklistStatusResult';
  id: Scalars['ID']['output'];
};

export type UpdateDraftBlacklistInput = {
  blacklistId?: InputMaybe<Scalars['ID']['input']>;
  correspondence?: InputMaybe<BlacklistDraftCorrespondenceInput>;
  document?: InputMaybe<BlacklistDraftDocumentInput>;
  packet?: InputMaybe<BlacklistPacketInput>;
  provider?: InputMaybe<BlacklistUpdateProviderInput>;
  skAdditionalData?: InputMaybe<BlacklistDraftSkAdditionalInput>;
  violation?: InputMaybe<BlacklistDraftViolationInput>;
};

export type UpdateDraftBlacklistResponse = GenericError | UpdateDraftBlacklistResult;

export type UpdateDraftBlacklistResult = {
  __typename?: 'UpdateDraftBlacklistResult';
  id: Scalars['ID']['output'];
};

export type User = PaKpa;

export type UserInfo = {
  __typename?: 'UserInfo';
  auth0UserID?: Maybe<Scalars['String']['output']>;
  authProviders?: Maybe<Array<Maybe<AuthProvider>>>;
  createdAt?: Maybe<Scalars['String']['output']>;
  createdBy?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isSeller?: Maybe<Scalars['Boolean']['output']>;
  login?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  nikHashed?: Maybe<Scalars['String']['output']>;
  nikToken?: Maybe<Scalars['String']['output']>;
  nipHashed?: Maybe<Scalars['String']['output']>;
  nipToken?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  profile?: Maybe<ProfileInfo>;
  profileStatus?: Maybe<ProfileStatus>;
  status?: Maybe<UserStatus>;
  type?: Maybe<Profession>;
  updatedAt?: Maybe<Scalars['String']['output']>;
  updatedBy?: Maybe<Scalars['String']['output']>;
  updatedIP?: Maybe<Scalars['String']['output']>;
  updatedUserAgent?: Maybe<Scalars['String']['output']>;
  username: Scalars['String']['output'];
};

export enum UserStatus {
  AsyncPending = 'ASYNC_PENDING',
  EmailSubmitted = 'EMAIL_SUBMITTED',
  EmailVerified = 'EMAIL_VERIFIED',
  PhoneVerified = 'PHONE_VERIFIED'
}

export type ValidateSkNumberInput = {
  excludeBlacklistIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  skNumber: Scalars['String']['input'];
};

export type ValidateSkNumberOutput = {
  __typename?: 'ValidateSkNumberOutput';
  invalidReason: Scalars['String']['output'];
  isValid: Scalars['Boolean']['output'];
};

export type ValidateTenderOwnerInput = {
  id: Scalars['ID']['input'];
  source: TenderSource;
};

export type ValidateTenderOwnerOutput = {
  __typename?: 'ValidateTenderOwnerOutput';
  isOwner: Scalars['Boolean']['output'];
  reason: Scalars['String']['output'];
};

export type Violation = {
  __typename?: 'Violation';
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  month: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  year: Scalars['Int']['output'];
};
